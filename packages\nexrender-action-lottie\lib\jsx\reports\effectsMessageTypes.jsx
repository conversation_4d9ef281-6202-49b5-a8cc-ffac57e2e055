/*jslint vars: true, plusplus: true, devel: true, nomen: true, regexp: true, indent: 4, maxerr: 50 */
/*global $, Folder, File, app */

$.__bodymovin.bm_reportsEffectMessages = (function () {
    
    var rendererTypes = $.__bodymovin.bm_reportRendererTypes;
    var messageTypes = $.__bodymovin.bm_reportMessageTypes;
    var builderTypes = $.__bodymovin.bm_reportBuilderTypes;

    var effectsMessages = {
        'ADBE AUX CHANNEL EXTRACT': { name: '3D Channel Extract', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE DEPTH MATTE': { name: 'Depth Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE DEPTH FIELD': { name: 'Depth of Field', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'EXtractoR': { name: 'EXtractoR', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE FOG_3D': { name: 'Fog 3D', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE ID MATTE': { name: 'ID Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'IDentifier': { name: 'IDentifier', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Reverse': { name: 'Backwards', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud BT': { name: 'Bass & Treble', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Delay': { name: 'Delay', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud_Flange': { name: 'Flange & Chorus', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud HiLo': { name: 'High-Low Pass', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Modulator': { name: 'Modulator', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Param EQ': { name: 'Parametric EQ', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Reverb': { name: 'Reverb', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Stereo Mixer': { name: 'Stereo Mixer', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Aud Tone': { name: 'Tone', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Bilateral': { name: 'Bilateral Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Camera Lens Blur': { name: 'Camera Lens Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE CameraShakeDeblur': { name: 'Camera-Shake Deblur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS CrossBlur': { name: 'CC Cross Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Radial Blur': { name: 'CC Radial Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Radial Fast Blur': { name: 'CC Radial Fast Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Vector Blur': { name: 'CC Vector Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Channel Blur': { name: 'Channel Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Compound Blur': { name: 'Compound Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Motion Blur': { name: 'Directional Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Box Blur2': { name: 'Fast Box Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Gaussian Blur 2': { name: 'Gaussian Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID]}]},
        'ADBE Radial Blur': { name: 'Radial Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Sharpen': { name: 'Sharpen', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Smart Blur': { name: 'Smart Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Unsharp Mask2': { name: 'Unsharp Mask', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Arithmetic': { name: 'Arithmetic', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Blend': { name: 'Blend', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Calculations': { name: 'Calculations', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Composite': { name: 'CC Composite', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Channel Combiner': { name: 'Channel Combiner', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Compound Arithmetic': { name: 'Compound Arithmetic', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Invert': { name: 'Invert', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Minimax': { name: 'Minimax', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Remove Color Matting': { name: 'Remove Color Matting', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Set Channels': { name: 'Set Channels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Set Matte3': { name: 'Set Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Shift Channels': { name: 'Shift Channels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Solid Composite': { name: 'Solid Composite', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CINEMA 4D Effect': { name: 'CINEWARE', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AutoColor': { name: 'Auto Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AutoContrast': { name: 'Auto Contrast', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AutoLevels': { name: 'Auto Levels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Black&White': { name: 'Black & White', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Brightness & Contrast 2': { name: 'Brightness & Contrast', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Broadcast Colors': { name: 'Broadcast Colors', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS Color Neutralizer': { name: 'CC Color Neutralizer', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Color Offset': { name: 'CC Color Offset', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS Kernel': { name: 'CC Kernel', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Toner': { name: 'CC Toner', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Change Color': { name: 'Change Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Change To Color': { name: 'Change to Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE CHANNEL MIXER': { name: 'Channel Mixer', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Balance 2': { name: 'Color Balance', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Balance (HLS)': { name: 'Color Balance (HLS)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Link': { name: 'Color Link', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Deflicker': { name: 'Color Stabilizer', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Colorama': { name: 'Colorama', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE CurvesCustom': { name: 'Curves', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Equalize': { name: 'Equalize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Exposure2': { name: 'Exposure', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Gamma/Pedestal/Gain2': { name: 'Gamma/Pedestal/Gain', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE HUE SATURATION': { name: 'Hue/Saturation', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Leave Color': { name: 'Leave Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Easy Levels2': { name: 'Levels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Pro Levels2': { name: 'Levels (Individual Controls)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Lumetri': { name: 'Lumetri Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PhotoFilterPS': { name: 'Photo Filter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PS Arbitrary Map': { name: 'PS Arbitrary Map', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE SelectiveColor': { name: 'Selective Color', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE ShadowHighlight': { name: 'Shadow/Highlight', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Tint': { name: 'Tint', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Tritone': { name: 'Tritone', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Vibrance': { name: 'Vibrance', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE BEZMESH': { name: 'Bezier Warp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Bulge': { name: 'Bulge', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Bend It': { name: 'CC Bend It', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Bender': { name: 'CC Bender', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Blobbylize': { name: 'CC Blobbylize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Flo Motion': { name: 'CC Flo Motion', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Griddler': { name: 'CC Griddler', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Lens': { name: 'CC Lens', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Page Turn': { name: 'CC Page Turn', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Power Pin': { name: 'CC Power Pin', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Ripple Pulse': { name: 'CC Ripple Pulse', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Slant': { name: 'CC Slant', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Smear': { name: 'CC Smear', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Split': { name: 'CC Split', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Split 2': { name: 'CC Split 2', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Tiler': { name: 'CC Tiler', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Corner Pin': { name: 'Corner Pin', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Upscale': { name: 'Detail-preserving Upscale', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Displacement Map': { name: 'Displacement Map', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE LIQUIFY': { name: 'Liquify', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Magnify': { name: 'Magnify', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE MESH WARP': { name: 'Mesh Warp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Mirror': { name: 'Mirror', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Offset': { name: 'Offset', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Optics Compensation': { name: 'Optics Compensation', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Polar Coordinates': { name: 'Polar Coordinates', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE RESHAPE': { name: 'Reshape', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Ripple': { name: 'Ripple', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Rolling Shutter': { name: 'Rolling Shutter Repair', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE SCHMEAR': { name: 'Smear', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Spherize': { name: 'Spherize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Geometry2': { name: 'Transform', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Turbulent Displace': { name: 'Turbulent Displace', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Twirl': { name: 'Twirl', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE WRPMESH': { name: 'Warp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE SubspaceStabilizer': { name: 'Warp Stabilizer VFX', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Wave Warp': { name: 'Wave Warp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Point3D Control': { name: '3D Point Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Angle Control': { name: 'Angle Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Checkbox Control': { name: 'Checkbox Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Control': { name: 'Color Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Layer Control': { name: 'Layer Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Point Control': { name: 'Point Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Slider Control': { name: 'Slider Control', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE 4ColorGradient': { name: '4-Color Gradient', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Lightning 2': { name: 'Advanced Lightning', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AudSpect': { name: 'Audio Spectrum', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AudWave': { name: 'Audio Waveform', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Laser': { name: 'Beam', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Glue Gun': { name: 'CC Glue Gun', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Light Burst 2.5': { name: 'CC Light Burst 2.5', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Light Rays': { name: 'CC Light Rays', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Light Sweep': { name: 'CC Light Sweep', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS Threads': { name: 'CC Threads', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Cell Pattern': { name: 'Cell Pattern', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Checkerboard': { name: 'Checkerboard', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Circle': { name: 'Circle', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE ELLIPSE': { name: 'Ellipse', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Eyedropper Fill': { name: 'Eyedropper Fill', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Fill': { name: 'Fill', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Fractal': { name: 'Fractal', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Ramp': { name: 'Gradient Ramp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Grid': { name: 'Grid', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Lens Flare': { name: 'Lens Flare', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Paint Bucket': { name: 'Paint Bucket', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Radio Waves': { name: 'Radio Waves', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Scribble Fill': { name: 'Scribble', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Stroke': { name: 'Stroke', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Vegas': { name: 'Vegas', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Write-on': { name: 'Write-on', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Spill2': { name: 'Advanced Spill Suppressor', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Simple Wire Removal': { name: 'CC Simple Wire Removal', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Difference Key': { name: 'Color Difference Key', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Range': { name: 'Color Range', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Difference Matte2': { name: 'Difference Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Extract': { name: 'Extract', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE ATG Extract': { name: 'Inner/Outer Key', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE KeyCleaner': { name: 'Key Cleaner', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'Keylight 906': { name: 'Keylight (1.2)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Linear Color Key2': { name: 'Linear Color Key', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Matte Choker': { name: 'Matte Choker', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ISL MochaShapeImporter': { name: 'mocha shape', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE RefineRBMatte': { name: 'Refine Hard Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE RefineMatte2': { name: 'Refine Soft Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Simple Choker': { name: 'Simple Choker', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'VISINF Grain Implant': { name: 'Add Grain', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Dust & Scratches': { name: 'Dust & Scratches', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Fractal Noise': { name: 'Fractal Noise', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'VISINF Grain Duplication': { name: 'Match Grain', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Median': { name: 'Median', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise': { name: 'Noise', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise Alpha2': { name: 'Noise Alpha', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise HLS2': { name: 'Noise HLS', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise HLS Auto2': { name: 'Noise HLS Auto', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'VISINF Grain Removal': { name: 'Remove Grain', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE AIF Perlin Noise 3D': { name: 'Turbulent Noise', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Basic 3D': { name: 'Basic 3D', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Basic Text2': { name: 'Basic Text', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Key': { name: 'Color Key', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Fast Blur': { name: 'Fast Blur (Legacy)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Gaussian Blur': { name: 'Gaussian Blur (Legacy)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Lightning': { name: 'Lightning', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Luma Key': { name: 'Luma Key', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Path Text': { name: 'Path Text', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Reduce Interlace Flicker': { name: 'Reduce Interlace Flicker', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Spill Suppressor': { name: 'Spill Suppressor', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE 3D Tracker': { name: '3D Camera Tracker', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE 3D Glasses2': { name: '3D Glasses', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Bevel Alpha': { name: 'Bevel Alpha', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Bevel Edges': { name: 'Bevel Edges', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Cylinder': { name: 'CC Cylinder', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Environment': { name: 'CC Environment', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Sphere': { name: 'CC Sphere', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Spotlight': { name: 'CC Spotlight', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Drop Shadow': { name: 'Drop Shadow', messages:[
            {type: messageTypes.ERROR, renderers:[rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]},
            {type: messageTypes.WARNING, renderers:[rendererTypes.BROWSER], builder: builderTypes.FILTER_SIZE},
        ]},
        'ADBE Radial Shadow': { name: 'Radial Shadow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC CardDanceCam': { name: 'Card Dance', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Caustics': { name: 'Caustics', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Ball Action': { name: 'CC Ball Action', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Bubbles': { name: 'CC Bubbles', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Drizzle': { name: 'CC Drizzle', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Hair': { name: 'CC Hair', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Mr. Mercury': { name: 'CC Mr. Mercury', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Particle Systems II': { name: 'CC Particle Systems II', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Particle World': { name: 'CC Particle World', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Pixel Polly': { name: 'CC Pixel Polly', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CSRainfall': { name: 'CC Rainfall', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Scatterize': { name: 'CC Scatterize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CSSnowfall': { name: 'CC Snowfall', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Star Burst': { name: 'CC Star Burst', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Foam': { name: 'Foam', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Playgnd': { name: 'Particle Playground', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Shatter': { name: 'Shatter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC Wave World': { name: 'Wave World', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Brush Strokes': { name: 'Brush Strokes', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Cartoonify': { name: 'Cartoon', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS BlockLoad': { name: 'CC Block Load', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Burn Film': { name: 'CC Burn Film', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Glass': { name: 'CC Glass', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS HexTile': { name: 'CC HexTile', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Kaleida': { name: 'CC Kaleida', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Mr. Smoothie': { name: 'CC Mr. Smoothie', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Plastic': { name: 'CC Plastic', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC RepeTile': { name: 'CC RepeTile', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Threshold': { name: 'CC Threshold', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Threshold RGB': { name: 'CC Threshold RGB', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS Vignette': { name: 'CC Vignette', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Emboss': { name: 'Color Emboss', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Emboss': { name: 'Emboss', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Find Edges': { name: 'Find Edges', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Glo2': { name: 'Glow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Mosaic': { name: 'Mosaic', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Tile': { name: 'Motion Tile', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Posterize': { name: 'Posterize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Roughen Edges': { name: 'Roughen Edges', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Scatter': { name: 'Scatter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Strobe': { name: 'Strobe Light', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Texturize': { name: 'Texturize', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Threshold2': { name: 'Threshold', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'SYNTHAP CF Color Finesse 2': { name: 'SA Color Finesse 3', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Numbers2': { name: 'Numbers', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Timecode': { name: 'Timecode', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Force Motion Blur': { name: 'CC Force Motion Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Wide Time': { name: 'CC Wide Time', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Echo': { name: 'Echo', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE OFMotionBlur': { name: 'Pixel Motion Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Posterize Time': { name: 'Posterize Time', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Difference': { name: 'Time Difference', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Time Displacement': { name: 'Time Displacement', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Timewarp': { name: 'Timewarp', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Block Dissolve': { name: 'Block Dissolve', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'APC CardWipeCam': { name: 'Card Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Glass Wipe': { name: 'CC Glass Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Grid Wipe': { name: 'CC Grid Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Image Wipe': { name: 'CC Image Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Jaws': { name: 'CC Jaws', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Light Wipe': { name: 'CC Light Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CS LineSweep': { name: 'CC Line Sweep', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Radial ScaleWipe': { name: 'CC Radial ScaleWipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Scale Wipe': { name: 'CC Scale Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Twister': { name: 'CC Twister', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC WarpoMatic': { name: 'CC WarpoMatic', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Gradient Wipe': { name: 'Gradient Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE IRIS_WIPE': { name: 'Iris Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Linear Wipe': { name: 'Linear Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Radial Wipe': { name: 'Radial Wipe', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Venetian Blinds': { name: 'Venetian Blinds', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Apply Color LUT2': { name: 'Apply Color LUT', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Overbrights': { name: 'CC Overbrights', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Cineon Converter2': { name: 'Cineon Converter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE ProfileToProfile': { name: 'Color Profile Converter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE GROW BOUNDS': { name: 'Grow Bounds', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Compander': { name: 'HDR Compander', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE HDR ToneMap': { name: 'HDR Highlight Compression', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Paint': { name: 'Paint', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Samurai': { name: 'Roto Brush & Refine Edge', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE FreePin3': { name: 'Puppet', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE RefineMatte': { name: 'Refine Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE 3D Glasses': { name: '3D Glasses (Obsolete)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Alpha Levels2': { name: 'Alpha Levels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Alpha Levels3': { name: 'Alpha Levels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Apply Color LUT': { name: 'Apply Color LUT', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Brightness & Contrast': { name: 'Brightness & Contrast', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Box Blur': { name: 'Box Blur', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Cineon Converter': { name: 'Cineon Converter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Color Balance': { name: 'Color Balance', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC PS Classic': { name: 'CC PS Classic (obsolete)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC PS LE Classic': { name: 'CC PS LE Classic (obsolete)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Rain': { name: 'CC Rain', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Snow': { name: 'CC Snow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Time Blend': { name: 'CC Time Blend', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'CC Time Blend FX': { name: 'CC Time Blend FX', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Exposure': { name: 'Exposure', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Easy Levels': { name: 'Levels', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Pro Levels': { name: 'Levels (Individual Controls)', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise Alpha': { name: 'Noise Alpha', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise HLS': { name: 'Noise HLS', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Noise HLS Auto': { name: 'Noise HLS Auto', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Bevel Emboss': { name: 'Photoshop Bevel And Emboss', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Drop Shadow': { name: 'Photoshop Drop Shadow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Inner Glow': { name: 'Photoshop Inner Glow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Inner Shadow': { name: 'Photoshop Inner Shadow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Outer Glow': { name: 'Photoshop Outer Glow', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE PSL Solid Fill': { name: 'Photoshop Solid Fill', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Photo Filter': { name: 'Photo Filter', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Set Matte2': { name: 'Set Matte', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Three-Way Color Corrector': { name: 'Three-Way Color Corrector', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Threshold': { name: 'Threshold', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Geometry': { name: 'Transform', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Unsharp Mask': { name: 'Unsharp Mask', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
        'ADBE Vector Paint': { name: 'Vector Paint', messages:[{type: messageTypes.ERROR, renderers:[rendererTypes.BROWSER, rendererTypes.IOS, rendererTypes.ANDROID, rendererTypes.SKOTTIE]}]},
    }

    return effectsMessages;
    
}());