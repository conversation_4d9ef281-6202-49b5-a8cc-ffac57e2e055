const lottieSettings = {
    segmented: false,
    segmentedTime: 10,
    standalone: false,
    avd: false,
    glyphs: false,
    includeExtraChars: false,
    bundleFonts: false,
    inlineFonts: false,
    hiddens: false,
    original_assets: true,
    original_names: false,
    should_encode_images: false,
    should_compress: false,
    should_skip_images: false,
    should_reuse_images: false,
    should_include_av_assets: false,
    compression_rate: 80,
    extraComps: {
        active: false,
        list: [],
    },
    guideds: false,
    ignore_expression_properties: false,
    export_old_format: false,
    use_source_names: false,
    shouldTrimData: false,
    skip_default_properties: false,
    not_supported_properties: false,
    pretty_print: false,
    useCompNamesAsIds: false,
    export_mode: "banner",
    export_modes: {
        standard: false,
        demo: false,
        standalone: false,
        banner: true,
        avd: false,
        smil: false,
        rive: false,
        reports: false,
    },
    demoData: {
        backgroundColor: "#ffffff",
    },
    banner: {
        lottie_origin: "local",
        lottie_path: "https://",
        lottie_library: "full",
        lottie_renderer: "svg",
        width: 500,
        height: 500,
        use_original_sizes: true,
        original_width: '/*MAIN_COMP.WIDTH*/',
        original_height: '/*MAIN_COMP.HEIGHT*/',
        click_tag: "#",
        zip_files: false,
        shouldIncludeAnimationDataInTemplate: true,
        shouldLoop: true,
        loopCount: 0,
        localPath: null,
    },
    expressions: {
        shouldBake: true,
        shouldCacheExport: false,
        shouldBakeBeyondWorkArea: false,
        sampleSize: 1,
    },
    audio: {
        isEnabled: false,
        shouldRaterizeWaveform: true,
        bitrate: "__bodymovin_sound_template_16",
    },
    metadata: {
        includeFileName: false,
        customProps: [],
    },
    template: {
        active: false,
        id: 0,
        errors: [],
    },
    essentialProperties: {
        active: true,
        useSlots: false,
        skipExternalComp: false,
    },
};

const lottiePaths = [
    {
        name: "Full",
        value: "full",
        fileSize: "60Kb",
        cdnjs: "https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie.min.js",
        local: "lottie.min.js",
        renderers: ["svg", "canvas", "html"],
    },
    {
        name: "Svg Full (Full svg renderer)",
        value: "svg_full",
        fileSize: "60Kb",
        cdnjs: "https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie_svg.min.js",
        local: "lottie_svg.min.js",
        renderers: ["svg"],
    },
    {
        name: "Svg Light (Svg renderer, no expressions or effects)",
        value: "svg_light",
        fileSize: "60Kb",
        cdnjs: "https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie_light.min.js",
        local: "lottie_light.min.js",
        renderers: ["svg"],
    },
    {
        name: "Canvas Full (Full canvas renderer)",
        value: "canvas_full",
        fileSize: "60Kb",
        cdnjs: "https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie_canvas.min.js",
        local: "lottie_canvas.min.js",
        renderers: ["canvas"],
    },
    {
        name: "Canvas Light (Canvas renderer, no expressions or effects)",
        value: "canvas_light",
        fileSize: "60Kb",
        cdnjs: "https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.6/lottie_light_canvas.min.js",
        local: "lottie_light_canvas.min.js",
        renderers: ["canvas"],
    },
];

module.exports = {
    lottiePaths,
    lottieSettings,
};
