{"name": "@nexrender/core", "version": "1.62.2", "main": "src/index.js", "author": "Inlife", "homepage": "https://www.nexrender.com", "bugs": "https://github.com/inlife/nexrender/issues", "repository": "https://github.com/inlife/nexrender.git", "scripts": {"pkg-prelink": "node ../../misc/prelink.js", "test": "mocha"}, "dependencies": {"@nexrender/types": "^1.62.0", "data-uri-to-buffer": "^3.0.0", "file-uri-to-path": "^2.0.0", "is-wsl": "^2.2.0", "make-fetch-happen": "^11.0.2", "mime-types": "^2.1.29", "mkdirp": "^1.0.4", "nanoid": "^3.2.0", "requireg": "^0.2.1", "rimraf": "^3.0.2", "strip-comments": "^2.0.1"}, "peerDependencies": {"@nexrender/action-cache": "^1.53.2", "@nexrender/action-compress": "^1.62.0", "@nexrender/action-copy": "^1.0.0", "@nexrender/action-decompress": "^1.48.2", "@nexrender/action-encode": "^1.1.4", "@nexrender/action-fonts": "^1.51.8", "@nexrender/action-image": "^1.49.4", "@nexrender/action-link": "^1.0.0", "@nexrender/action-lottie": "^1.62.1", "@nexrender/action-mogrt": "^1.0.2", "@nexrender/action-upload": "^1.0.0", "@nexrender/action-webhook": "^1.51.3", "@nexrender/provider-ftp": "^1.17.2", "@nexrender/provider-gs": "^1.21.3", "@nexrender/provider-nx": "^1.47.0", "@nexrender/provider-s3": "^1.21.4", "@nexrender/provider-sftp": "^1.37.9"}, "publishConfig": {"access": "public"}, "gitHead": "441c3e3127cb50b603263fd560b617482b31eda8"}