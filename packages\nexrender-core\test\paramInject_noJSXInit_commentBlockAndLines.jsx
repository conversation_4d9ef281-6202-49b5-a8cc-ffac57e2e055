{
    // The following line should be ignored by the script parameter injection.
    // var NX = NX || { name: "<PERSON>" };
    //var NX = NX || { name: "<PERSON>" };
    /* var NX = NX || { name: "<PERSON>" }; */
    /* 
    * var NX = NX || { name: "<PERSON>" };
    *var NX = NX || { name: "<PERSON>" };
     */
    /*
    var NX = NX || { name : "<PERSON>" };
    */
    alert("Hello " + NX.name);
}