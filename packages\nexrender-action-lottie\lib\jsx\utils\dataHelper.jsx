
/*jslint vars: true , plusplus: true, continue:true, devel: true, nomen: true, regexp: true, indent: 4, maxerr: 50 */
/*global $*/
$.__bodymovin.bm_dataHelper = (function () {
    var bm_keyframeHelper = $.__bodymovin.bm_keyframeHelper;
    var bm_eventDispatcher = $.__bodymovin.bm_eventDispatcher;
    var settingsHelper = $.__bodymovin.bm_settingsHelper;
    var bm_generalUtils = $.__bodymovin.bm_generalUtils;
    var ob = {};
    
    function exportData(layerInfo, data, frameRate) {
        
    }
    
    ob.exportData = exportData;
    
    return ob;
}());
    