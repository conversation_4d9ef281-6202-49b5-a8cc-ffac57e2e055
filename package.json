{"name": "nexrender", "private": true, "version": "1.0.0", "homepage": "https://www.nexrender.com", "description": "Automate your After Effects project creation and rendering routine.", "repository": "inlife/nexrender", "license": "MIT", "author": {"name": "inlife", "email": "vlad<PERSON><PERSON><PERSON>+<EMAIL>", "url": "https://github.com/inlife"}, "scripts": {"start": "npm run bootstrap", "install-child": "lerna exec -- npm i", "bootstrap": "lerna bootstrap --no-ci -- --no-package-lock", "lint": "eslint .", "purge-cache": "lerna exec 'rm -rf node_modules && rm -f package-lock.json'", "purge-master": "rm -rf node_modules && rm -f package-lock.json", "publish": "lerna <PERSON>", "public-publish": "lerna exec 'npm publish --access public'", "pkg-rename": "node misc/rename.js", "pkg-cli": "cross-env NEXRENDER_REQUIRE_PLUGINS=1 pkg packages/nexrender-cli/package.json --out-path bin/", "pkg-worker": "cross-env NEXRENDER_REQUIRE_PLUGINS=1 pkg packages/nexrender-worker/package.json --out-path bin/", "pkg-server": "cross-env NEXRENDER_REQUIRE_PLUGINS=1 pkg packages/nexrender-server/package.json --out-path bin/", "pkg-prelink": "lerna run --scope @nexrender/core pkg-prelink && lerna run --scope @nexrender/server pkg-prelink", "pkg": "npm run pkg-prelink && npm run pkg-cli && npm run pkg-server && npm run pkg-worker && npm run pkg-rename", "test": "npm run lint && mocha"}, "devDependencies": {"chai": "^4.3.10", "cross-env": "^5.2.0", "eslint": "^8.6.0", "lerna": "^3.21.0", "mocha": "^7.1.2", "pkg": "^5.8.1", "testdouble": "^3.20.0"}}