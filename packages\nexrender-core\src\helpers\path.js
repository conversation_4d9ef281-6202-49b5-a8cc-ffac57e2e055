/**
 * Expand environment variables
 * Example:
 * Assuming $NEXRENDER_ASSETS is set to /Users/<USER>/nexrender
 * in the environment of the current process
 * an input of file://$NEXRENDER_ASSETS/projects/project2.aep
 * would output: file:///Users/<USER>/nexrender/projects/project2.aep
 */
function expandEnvironmentVariables(pathString) {
    const sigiledStrings = pathString.match(/\$.\w*/g) || [];

    return sigiledStrings.reduce((accumulator, sigiledString) => {
        const potentialEnvVarKey = sigiledString.replace("$", "");

        if (process.env.hasOwnProperty(potentialEnvVarKey)) {
            return accumulator.replace(
                sigiledString,
                process.env[potentialEnvVarKey]
            );
        } else {
            return accumulator;
        }
    }, pathString);
}

let checkForWSL = (pathString, settings) => {
    if (!settings.wsl) return pathString

    return pathString.match(/\/mnt\/[a-zA-Z]\//)
        ? pathString.replace(
              /\/mnt\/[a-zA-Z]\//,
              `${pathString.split('/')[2].toUpperCase()}:/`
          )
        : `${settings.wslMap}:${pathString}`
}

module.exports = {
    expandEnvironmentVariables: expandEnvironmentVariables,
    checkForWSL
};
