{"name": "@nexrender/server", "version": "1.62.0", "author": "inlife", "main": "src/index.js", "homepage": "https://www.nexrender.com", "bugs": "https://github.com/inlife/nexrender/issues", "repository": "https://github.com/inlife/nexrender.git", "scripts": {"start": "node src/bin.js", "pkg-prelink": "node ../../misc/prelink.js"}, "bin": {"nexrender-server": "src/bin.js"}, "pkg": {"targets": ["node18-macos-x64", "node18-linux-x64", "node18-win-x64"]}, "dependencies": {"@nexrender/database-redis": "^1.60.10", "@nexrender/types": "^1.62.0", "arg": "^4.1.0", "async-mutex": "^0.3.2", "chalk": "^2.4.2", "micro": "~9.3.3", "micro-cors": "^0.1.1", "microrouter": "^3.1.3", "requireg": "^0.2.2"}, "peerDependencies": {"@nexrender/database-redis": "^1.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "84cef65e008026f6fcf4f040943144401f101fce"}