// Generated by browserify
(function(){var require = function (file, cwd) {
    var resolved = require.resolve(file, cwd || '/');
    var mod = require.modules[resolved];
    if (!mod) throw new Error(
        'Failed to resolve module ' + file + ', tried ' + resolved
    );
    var cached = require.cache[resolved];
    var res = cached? cached.exports : mod();
    return res;
};

    require.paths = [];
    require.modules = {};
    require.cache = {};
    require.extensions = [".js",".coffee",".json"];

    require._core = {
        'assert': true,
        'events': true,
        'fs': true,
        'path': true,
        'vm': true
    };

    require.resolve = (function () {
        return function (x, cwd) {
            if (!cwd) cwd = '/';

            if (require._core[x]) return x;
            var path = require.modules.path();
            cwd = path.resolve('/', cwd);
            var y = cwd || '/';

            if (x.match(/^(?:\.\.?\/|\/)/)) {
                var m = loadAsFileSync(path.resolve(y, x))
                    || loadAsDirectorySync(path.resolve(y, x));
                if (m) return m;
            }

            var n = loadNodeModulesSync(x, y);
            if (n) return n;

            throw new Error("Cannot find module '" + x + "'");

            function loadAsFileSync (x) {
                x = path.normalize(x);
                if (require.modules[x]) {
                    return x;
                }

                for (var i = 0; i < require.extensions.length; i++) {
                    var ext = require.extensions[i];
                    if (require.modules[x + ext]) return x + ext;
                }
            }

            function loadAsDirectorySync (x) {
                x = x.replace(/\/+$/, '');
                var pkgfile = path.normalize(x + '/package.json');
                if (require.modules[pkgfile]) {
                    var pkg = require.modules[pkgfile]();
                    var b = pkg.browserify;
                    if (typeof b === 'object' && b.main) {
                        var m = loadAsFileSync(path.resolve(x, b.main));
                        if (m) return m;
                    }
                    else if (typeof b === 'string') {
                        var m = loadAsFileSync(path.resolve(x, b));
                        if (m) return m;
                    }
                    else if (pkg.main) {
                        var m = loadAsFileSync(path.resolve(x, pkg.main));
                        if (m) return m;
                    }
                }

                return loadAsFileSync(x + '/index');
            }

            function loadNodeModulesSync (x, start) {
                var dirs = nodeModulesPathsSync(start);
                for (var i = 0; i < dirs.length; i++) {
                    var dir = dirs[i];
                    var m = loadAsFileSync(dir + '/' + x);
                    if (m) return m;
                    var n = loadAsDirectorySync(dir + '/' + x);
                    if (n) return n;
                }

                var m = loadAsFileSync(x);
                if (m) return m;
            }

            function nodeModulesPathsSync (start) {
                var parts;
                if (start === '/') parts = [ '' ];
                else parts = path.normalize(start).split('/');

                var dirs = [];
                for (var i = parts.length - 1; i >= 0; i--) {
                    if (parts[i] === 'node_modules') continue;
                    var dir = parts.slice(0, i + 1).join('/') + '/node_modules';
                    dirs.push(dir);
                }

                return dirs;
            }
        };
    })();

    require.alias = function (from, to) {
        var path = require.modules.path();
        var res = null;
        try {
            res = require.resolve(from + '/package.json', '/');
        }
        catch (err) {
            res = require.resolve(from, '/');
        }
        var basedir = path.dirname(res);

        var keys = (Object.keys || function (obj) {
            var res = [];
            for (var key in obj) res.push(key);
            return res;
        })(require.modules);

        for (var i = 0; i < keys.length; i++) {
            var key = keys[i];
            if (key.slice(0, basedir.length + 1) === basedir + '/') {
                var f = key.slice(basedir.length);
                require.modules[to + f] = require.modules[basedir + f];
            }
            else if (key === basedir) {
                require.modules[to] = require.modules[basedir];
            }
        }
    };

    (function () {
        var process = {};
        var global = typeof window !== 'undefined' ? window : {};
        var definedProcess = false;

        require.define = function (filename, fn) {
            if (!definedProcess && require.modules.__browserify_process) {
                process = require.modules.__browserify_process();
                definedProcess = true;
            }

            var dirname = require._core[filename]
                    ? ''
                    : require.modules.path().dirname(filename)
                ;

            var require_ = function (file) {
                var requiredModule = require(file, dirname);
                var cached = require.cache[require.resolve(file, dirname)];

                if (cached && cached.parent === null) {
                    cached.parent = module_;
                }

                return requiredModule;
            };
            require_.resolve = function (name) {
                return require.resolve(name, dirname);
            };
            require_.modules = require.modules;
            require_.define = require.define;
            require_.cache = require.cache;
            var module_ = {
                id : filename,
                filename: filename,
                exports : {},
                loaded : false,
                parent: null
            };

            require.modules[filename] = function () {
                require.cache[filename] = module_;
                fn.call(
                    module_.exports,
                    require_,
                    module_,
                    module_.exports,
                    dirname,
                    filename,
                    process,
                    global
                );
                module_.loaded = true;
                return module_.exports;
            };
        };
    })();


    require.define("path",function(require,module,exports,__dirname,__filename,process,global){function filter (xs, fn) {
        var res = [];
        for (var i = 0; i < xs.length; i++) {
            if (fn(xs[i], i, xs)) res.push(xs[i]);
        }
        return res;
    }

// resolves . and .. elements in a path array with directory names there
// must be no slashes, empty elements, or device names (c:\) in the array
// (so also no leading and trailing slashes - it does not distinguish
// relative and absolute paths)
        function normalizeArray(parts, allowAboveRoot) {
            // if the path tries to go above the root, `up` ends up > 0
            var up = 0;
            for (var i = parts.length; i >= 0; i--) {
                var last = parts[i];
                if (last == '.') {
                    parts.splice(i, 1);
                } else if (last === '..') {
                    parts.splice(i, 1);
                    up++;
                } else if (up) {
                    parts.splice(i, 1);
                    up--;
                }
            }

            // if the path is allowed to go above the root, restore leading ..s
            if (allowAboveRoot) {
                for (; up--; up) {
                    parts.unshift('..');
                }
            }

            return parts;
        }

// Regex to split a filename into [*, dir, basename, ext]
// posix version
        var splitPathRe = /^(.+\/(?!$)|\/)?((?:.+?)?(\.[^.]*)?)$/;

// path.resolve([from ...], to)
// posix version
        exports.resolve = function() {
            var resolvedPath = '',
                resolvedAbsolute = false;

            for (var i = arguments.length; i >= -1 && !resolvedAbsolute; i--) {
                var path = (i >= 0)
                    ? arguments[i]
                    : process.cwd();

                // Skip empty and invalid entries
                if (typeof path !== 'string' || !path) {
                    continue;
                }

                resolvedPath = path + '/' + resolvedPath;
                resolvedAbsolute = path.charAt(0) === '/';
            }

// At this point the path should be resolved to a full absolute path, but
// handle relative paths to be safe (might happen when process.cwd() fails)

// Normalize the path
            resolvedPath = normalizeArray(filter(resolvedPath.split('/'), function(p) {
                return !!p;
            }), !resolvedAbsolute).join('/');

            return ((resolvedAbsolute ? '/' : '') + resolvedPath) || '.';
        };

// path.normalize(path)
// posix version
        exports.normalize = function(path) {
            var isAbsolute = path.charAt(0) === '/',
                trailingSlash = path.slice(-1) === '/';

// Normalize the path
            path = normalizeArray(filter(path.split('/'), function(p) {
                return !!p;
            }), !isAbsolute).join('/');

            if (!path && !isAbsolute) {
                path = '.';
            }
            if (path && trailingSlash) {
                path += '/';
            }

            return (isAbsolute ? '/' : '') + path;
        };


// posix version
        exports.join = function() {
            var paths = Array.prototype.slice.call(arguments, 0);
            return exports.normalize(filter(paths, function(p, index) {
                return p && typeof p === 'string';
            }).join('/'));
        };


        exports.dirname = function(path) {
            var dir = splitPathRe.exec(path)[1] || '';
            var isWindows = false;
            if (!dir) {
                // No dirname
                return '.';
            } else if (dir.length === 1 ||
                (isWindows && dir.length <= 3 && dir.charAt(1) === ':')) {
                // It is just a slash or a drive letter with a slash
                return dir;
            } else {
                // It is a full dirname, strip trailing slash
                return dir.substring(0, dir.length - 1);
            }
        };


        exports.basename = function(path, ext) {
            var f = splitPathRe.exec(path)[2] || '';
            // TODO: make this comparison case-insensitive on windows?
            if (ext && f.substr(-1 * ext.length) === ext) {
                f = f.substr(0, f.length - ext.length);
            }
            return f;
        };


        exports.extname = function(path) {
            return splitPathRe.exec(path)[3] || '';
        };

        exports.relative = function(from, to) {
            from = exports.resolve(from).substr(1);
            to = exports.resolve(to).substr(1);

            function trim(arr) {
                var start = 0;
                for (; start < arr.length; start++) {
                    if (arr[start] !== '') break;
                }

                var end = arr.length - 1;
                for (; end >= 0; end--) {
                    if (arr[end] !== '') break;
                }

                if (start > end) return [];
                return arr.slice(start, end - start + 1);
            }

            var fromParts = trim(from.split('/'));
            var toParts = trim(to.split('/'));

            var length = Math.min(fromParts.length, toParts.length);
            var samePartsLength = length;
            for (var i = 0; i < length; i++) {
                if (fromParts[i] !== toParts[i]) {
                    samePartsLength = i;
                    break;
                }
            }

            var outputParts = [];
            for (var i = samePartsLength; i < fromParts.length; i++) {
                outputParts.push('..');
            }

            outputParts = outputParts.concat(toParts.slice(samePartsLength));

            return outputParts.join('/');
        };

    });

    require.define("__browserify_process",function(require,module,exports,__dirname,__filename,process,global){var process = module.exports = {};

        process.nextTick = (function () {
            var canSetImmediate = typeof window !== 'undefined'
                && window.setImmediate;
            var canPost = typeof window !== 'undefined'
                    && window.postMessage && window.addEventListener
                ;

            if (canSetImmediate) {
                return function (f) { return window.setImmediate(f) };
            }

            if (canPost) {
                var queue = [];
                window.addEventListener('message', function (ev) {
                    if (ev.source === window && ev.data === 'browserify-tick') {
                        ev.stopPropagation();
                        if (queue.length > 0) {
                            var fn = queue.shift();
                            fn();
                        }
                    }
                }, true);

                return function nextTick(fn) {
                    queue.push(fn);
                    window.postMessage('browserify-tick', '*');
                };
            }

            return function nextTick(fn) {
                setTimeout(fn, 0);
            };
        })();

        process.title = 'browser';
        process.browser = true;
        process.env = {};
        process.argv = [];

        process.binding = function (name) {
            if (name === 'evals') return (require)('vm')
            else throw new Error('No such module. (Possibly not yet loaded)')
        };

        (function () {
            var cwd = '/';
            var path;
            process.cwd = function () { return cwd };
            process.chdir = function (dir) {
                if (!path) path = require('path');
                cwd = path.resolve(dir, cwd);
            };
        })();

    });

    require.define("/package.json",function(require,module,exports,__dirname,__filename,process,global){module.exports = {"main":"escodegen.js"}
    });

    require.define("/escodegen.js",function(require,module,exports,__dirname,__filename,process,global){/*
     Copyright (C) 2012 Michael Ficarra <<EMAIL>>
     Copyright (C) 2012 Robert Gust-Bardon <<EMAIL>>
     Copyright (C) 2012 John Freeman <<EMAIL>>
     Copyright (C) 2011-2012 Ariya Hidayat <<EMAIL>>
     Copyright (C) 2012 Mathias Bynens <<EMAIL>>
     Copyright (C) 2012 Joost-Wim Boekesteijn <<EMAIL>>
     Copyright (C) 2012 Kris Kowal <<EMAIL>>
     Copyright (C) 2012 Yusuke Suzuki <<EMAIL>>
     Copyright (C) 2012 Arpad Borsos <<EMAIL>>

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are met:

     * Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

     THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
     AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
     IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
     ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
     DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
     THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */

        /*jslint bitwise:true */
        /*global escodegen:true, exports:true, generateStatement:true, generateExpression:true, generateFunctionBody:true, process:true, require:true, define:true, global:true*/
        (function () {
            'use strict';

            var Syntax,
                Precedence,
                BinaryPrecedence,
                Regex,
                VisitorKeys,
                VisitorOption,
                SourceNode,
                isArray,
                base,
                indent,
                json,
                renumber,
                hexadecimal,
                quotes,
                escapeless,
                newline,
                space,
                parentheses,
                semicolons,
                safeConcatenation,
                directive,
                extra,
                parse,
                sourceMap,
                traverse;

            traverse = require('estraverse').traverse;

            Syntax = {
                AssignmentExpression: 'AssignmentExpression',
                ArrayExpression: 'ArrayExpression',
                ArrayPattern: 'ArrayPattern',
                BlockStatement: 'BlockStatement',
                BinaryExpression: 'BinaryExpression',
                BreakStatement: 'BreakStatement',
                CallExpression: 'CallExpression',
                CatchClause: 'CatchClause',
                ComprehensionBlock: 'ComprehensionBlock',
                ComprehensionExpression: 'ComprehensionExpression',
                ConditionalExpression: 'ConditionalExpression',
                ContinueStatement: 'ContinueStatement',
                DirectiveStatement: 'DirectiveStatement',
                DoWhileStatement: 'DoWhileStatement',
                DebuggerStatement: 'DebuggerStatement',
                EmptyStatement: 'EmptyStatement',
                ExpressionStatement: 'ExpressionStatement',
                ForStatement: 'ForStatement',
                ForInStatement: 'ForInStatement',
                FunctionDeclaration: 'FunctionDeclaration',
                FunctionExpression: 'FunctionExpression',
                Identifier: 'Identifier',
                IfStatement: 'IfStatement',
                Literal: 'Literal',
                LabeledStatement: 'LabeledStatement',
                LogicalExpression: 'LogicalExpression',
                MemberExpression: 'MemberExpression',
                NewExpression: 'NewExpression',
                ObjectExpression: 'ObjectExpression',
                ObjectPattern: 'ObjectPattern',
                Program: 'Program',
                Property: 'Property',
                ReturnStatement: 'ReturnStatement',
                SequenceExpression: 'SequenceExpression',
                SwitchStatement: 'SwitchStatement',
                SwitchCase: 'SwitchCase',
                ThisExpression: 'ThisExpression',
                ThrowStatement: 'ThrowStatement',
                TryStatement: 'TryStatement',
                UnaryExpression: 'UnaryExpression',
                UpdateExpression: 'UpdateExpression',
                VariableDeclaration: 'VariableDeclaration',
                VariableDeclarator: 'VariableDeclarator',
                WhileStatement: 'WhileStatement',
                WithStatement: 'WithStatement',
                YieldExpression: 'YieldExpression'

            };

            Precedence = {
                Sequence: 0,
                Assignment: 1,
                Conditional: 2,
                LogicalOR: 3,
                LogicalAND: 4,
                BitwiseOR: 5,
                BitwiseXOR: 6,
                BitwiseAND: 7,
                Equality: 8,
                Relational: 9,
                BitwiseSHIFT: 10,
                Additive: 11,
                Multiplicative: 12,
                Unary: 13,
                Postfix: 14,
                Call: 15,
                New: 16,
                Member: 17,
                Primary: 18
            };

            BinaryPrecedence = {
                '||': Precedence.LogicalOR,
                '&&': Precedence.LogicalAND,
                '|': Precedence.BitwiseOR,
                '^': Precedence.BitwiseXOR,
                '&': Precedence.BitwiseAND,
                '==': Precedence.Equality,
                '!=': Precedence.Equality,
                '===': Precedence.Equality,
                '!==': Precedence.Equality,
                'is': Precedence.Equality,
                'isnt': Precedence.Equality,
                '<': Precedence.Relational,
                '>': Precedence.Relational,
                '<=': Precedence.Relational,
                '>=': Precedence.Relational,
                'in': Precedence.Relational,
                'instanceof': Precedence.Relational,
                '<<': Precedence.BitwiseSHIFT,
                '>>': Precedence.BitwiseSHIFT,
                '>>>': Precedence.BitwiseSHIFT,
                '+': Precedence.Additive,
                '-': Precedence.Additive,
                '*': Precedence.Multiplicative,
                '%': Precedence.Multiplicative,
                '/': Precedence.Multiplicative
            };

            Regex = {
                NonAsciiIdentifierPart: new RegExp('[\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0300-\u0374\u0376\u0377\u037a-\u037d\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u0483-\u0487\u048a-\u0527\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u05d0-\u05ea\u05f0-\u05f2\u0610-\u061a\u0620-\u0669\u066e-\u06d3\u06d5-\u06dc\u06df-\u06e8\u06ea-\u06fc\u06ff\u0710-\u074a\u074d-\u07b1\u07c0-\u07f5\u07fa\u0800-\u082d\u0840-\u085b\u08a0\u08a2-\u08ac\u08e4-\u08fe\u0900-\u0963\u0966-\u096f\u0971-\u0977\u0979-\u097f\u0981-\u0983\u0985-\u098c\u098f\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bc-\u09c4\u09c7\u09c8\u09cb-\u09ce\u09d7\u09dc\u09dd\u09df-\u09e3\u09e6-\u09f1\u0a01-\u0a03\u0a05-\u0a0a\u0a0f\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32\u0a33\u0a35\u0a36\u0a38\u0a39\u0a3c\u0a3e-\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a59-\u0a5c\u0a5e\u0a66-\u0a75\u0a81-\u0a83\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2\u0ab3\u0ab5-\u0ab9\u0abc-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ad0\u0ae0-\u0ae3\u0ae6-\u0aef\u0b01-\u0b03\u0b05-\u0b0c\u0b0f\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32\u0b33\u0b35-\u0b39\u0b3c-\u0b44\u0b47\u0b48\u0b4b-\u0b4d\u0b56\u0b57\u0b5c\u0b5d\u0b5f-\u0b63\u0b66-\u0b6f\u0b71\u0b82\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99\u0b9a\u0b9c\u0b9e\u0b9f\u0ba3\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd0\u0bd7\u0be6-\u0bef\u0c01-\u0c03\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c33\u0c35-\u0c39\u0c3d-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c58\u0c59\u0c60-\u0c63\u0c66-\u0c6f\u0c82\u0c83\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbc-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5\u0cd6\u0cde\u0ce0-\u0ce3\u0ce6-\u0cef\u0cf1\u0cf2\u0d02\u0d03\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d-\u0d44\u0d46-\u0d48\u0d4a-\u0d4e\u0d57\u0d60-\u0d63\u0d66-\u0d6f\u0d7a-\u0d7f\u0d82\u0d83\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0df2\u0df3\u0e01-\u0e3a\u0e40-\u0e4e\u0e50-\u0e59\u0e81\u0e82\u0e84\u0e87\u0e88\u0e8a\u0e8d\u0e94-\u0e97\u0e99-\u0e9f\u0ea1-\u0ea3\u0ea5\u0ea7\u0eaa\u0eab\u0ead-\u0eb9\u0ebb-\u0ebd\u0ec0-\u0ec4\u0ec6\u0ec8-\u0ecd\u0ed0-\u0ed9\u0edc-\u0edf\u0f00\u0f18\u0f19\u0f20-\u0f29\u0f35\u0f37\u0f39\u0f3e-\u0f47\u0f49-\u0f6c\u0f71-\u0f84\u0f86-\u0f97\u0f99-\u0fbc\u0fc6\u1000-\u1049\u1050-\u109d\u10a0-\u10c5\u10c7\u10cd\u10d0-\u10fa\u10fc-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u135d-\u135f\u1380-\u138f\u13a0-\u13f4\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f0\u1700-\u170c\u170e-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176c\u176e-\u1770\u1772\u1773\u1780-\u17d3\u17d7\u17dc\u17dd\u17e0-\u17e9\u180b-\u180d\u1810-\u1819\u1820-\u1877\u1880-\u18aa\u18b0-\u18f5\u1900-\u191c\u1920-\u192b\u1930-\u193b\u1946-\u196d\u1970-\u1974\u1980-\u19ab\u19b0-\u19c9\u19d0-\u19d9\u1a00-\u1a1b\u1a20-\u1a5e\u1a60-\u1a7c\u1a7f-\u1a89\u1a90-\u1a99\u1aa7\u1b00-\u1b4b\u1b50-\u1b59\u1b6b-\u1b73\u1b80-\u1bf3\u1c00-\u1c37\u1c40-\u1c49\u1c4d-\u1c7d\u1cd0-\u1cd2\u1cd4-\u1cf6\u1d00-\u1de6\u1dfc-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u200c\u200d\u203f\u2040\u2054\u2071\u207f\u2090-\u209c\u20d0-\u20dc\u20e1\u20e5-\u20f0\u2102\u2107\u210a-\u2113\u2115\u2119-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cf3\u2d00-\u2d25\u2d27\u2d2d\u2d30-\u2d67\u2d6f\u2d7f-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2de0-\u2dff\u2e2f\u3005-\u3007\u3021-\u302f\u3031-\u3035\u3038-\u303c\u3041-\u3096\u3099\u309a\u309d-\u309f\u30a1-\u30fa\u30fc-\u30ff\u3105-\u312d\u3131-\u318e\u31a0-\u31ba\u31f0-\u31ff\u3400-\u4db5\u4e00-\u9fcc\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua62b\ua640-\ua66f\ua674-\ua67d\ua67f-\ua697\ua69f-\ua6f1\ua717-\ua71f\ua722-\ua788\ua78b-\ua78e\ua790-\ua793\ua7a0-\ua7aa\ua7f8-\ua827\ua840-\ua873\ua880-\ua8c4\ua8d0-\ua8d9\ua8e0-\ua8f7\ua8fb\ua900-\ua92d\ua930-\ua953\ua960-\ua97c\ua980-\ua9c0\ua9cf-\ua9d9\uaa00-\uaa36\uaa40-\uaa4d\uaa50-\uaa59\uaa60-\uaa76\uaa7a\uaa7b\uaa80-\uaac2\uaadb-\uaadd\uaae0-\uaaef\uaaf2-\uaaf6\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uabc0-\uabea\uabec\uabed\uabf0-\uabf9\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\uf900-\ufa6d\ufa70-\ufad9\ufb00-\ufb06\ufb13-\ufb17\ufb1d-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40\ufb41\ufb43\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe00-\ufe0f\ufe20-\ufe26\ufe33\ufe34\ufe4d-\ufe4f\ufe70-\ufe74\ufe76-\ufefc\uff10-\uff19\uff21-\uff3a\uff3f\uff41-\uff5a\uff66-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]')
            };

            function getDefaultOptions() {
                // default options
                return {
                    indent: null,
                    base: null,
                    parse: null,
                    comment: false,
                    format: {
                        indent: {
                            style: '    ',
                            base: 0,
                            adjustMultilineComment: false
                        },
                        json: false,
                        renumber: false,
                        hexadecimal: false,
                        quotes: 'single',
                        escapeless: false,
                        compact: false,
                        parentheses: true,
                        semicolons: true,
                        safeConcatenation: false
                    },
                    moz: {
                        starlessGenerator: false,
                        parenthesizedComprehensionBlock: false
                    },
                    sourceMap: null,
                    sourceMapRoot: null,
                    sourceMapWithCode: false,
                    directive: false,
                    verbatim: null
                };
            }

            function stringToArray(str) {
                var length = str.length,
                    result = [],
                    i;
                for (i = 0; i < length; i += 1) {
                    result[i] = str.charAt(i);
                }
                return result;
            }

            function stringRepeat(str, num) {
                var result = '';

                for (num |= 0; num > 0; num >>>= 1, str += str) {
                    if (num & 1) {
                        result += str;
                    }
                }

                return result;
            }

            isArray = Array.isArray;
            if (!isArray) {
                isArray = function isArray(array) {
                    return Object.prototype.toString.call(array) === '[object Array]';
                };
            }

            // Fallback for the non SourceMap environment
            function SourceNodeMock(line, column, filename, chunk) {
                var result = [];

                function flatten(input) {
                    var i, iz;
                    if (isArray(input)) {
                        for (i = 0, iz = input.length; i < iz; ++i) {
                            flatten(input[i]);
                        }
                    } else if (input instanceof SourceNodeMock) {
                        result.push(input);
                    } else if (typeof input === 'string' && input) {
                        result.push(input);
                    }
                }

                flatten(chunk);
                this.children = result;
            }

            SourceNodeMock.prototype.toString = function toString() {
                var res = '', i, iz, node;
                for (i = 0, iz = this.children.length; i < iz; ++i) {
                    node = this.children[i];
                    if (node instanceof SourceNodeMock) {
                        res += node.toString();
                    } else {
                        res += node;
                    }
                }
                return res;
            };

            SourceNodeMock.prototype.replaceRight = function replaceRight(pattern, replacement) {
                var last = this.children[this.children.length - 1];
                if (last instanceof SourceNodeMock) {
                    last.replaceRight(pattern, replacement);
                } else if (typeof last === 'string') {
                    this.children[this.children.length - 1] = last.replace(pattern, replacement);
                } else {
                    this.children.push(''.replace(pattern, replacement));
                }
                return this;
            };

            SourceNodeMock.prototype.join = function join(sep) {
                var i, iz, result;
                result = [];
                iz = this.children.length;
                if (iz > 0) {
                    for (i = 0, iz -= 1; i < iz; ++i) {
                        result.push(this.children[i], sep);
                    }
                    result.push(this.children[iz]);
                    this.children = result;
                }
                return this;
            };

            function hasLineTerminator(str) {
                return (/[\r\n]/g).test(str);
            }

            function endsWithLineTerminator(str) {
                var ch = str.charAt(str.length - 1);
                return ch === '\r' || ch === '\n';
            }

            function shallowCopy(obj) {
                var ret = {}, key;
                for (key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        ret[key] = obj[key];
                    }
                }
                return ret;
            }

            function deepCopy(obj) {
                var ret = {}, key, val;
                for (key in obj) {
                    if (obj.hasOwnProperty(key)) {
                        val = obj[key];
                        if (typeof val === 'object' && val !== null) {
                            ret[key] = deepCopy(val);
                        } else {
                            ret[key] = val;
                        }
                    }
                }
                return ret;
            }

            function updateDeeply(target, override) {
                var key, val;

                function isHashObject(target) {
                    return typeof target === 'object' && target instanceof Object && !(target instanceof RegExp);
                }

                for (key in override) {
                    if (override.hasOwnProperty(key)) {
                        val = override[key];
                        if (isHashObject(val)) {
                            if (isHashObject(target[key])) {
                                updateDeeply(target[key], val);
                            } else {
                                target[key] = updateDeeply({}, val);
                            }
                        } else {
                            target[key] = val;
                        }
                    }
                }
                return target;
            }

            function generateNumber(value) {
                var result, point, temp, exponent, pos;

                if (value !== value) {
                    throw new Error('Numeric literal whose value is NaN');
                }
                if (value < 0 || (value === 0 && 1 / value < 0)) {
                    throw new Error('Numeric literal whose value is negative');
                }

                if (value === 1 / 0) {
                    return json ? 'null' : renumber ? '1e400' : '1e+400';
                }

                result = '' + value;
                if (!renumber || result.length < 3) {
                    return result;
                }

                point = result.indexOf('.');
                if (!json && result.charAt(0) === '0' && point === 1) {
                    point = 0;
                    result = result.slice(1);
                }
                temp = result;
                result = result.replace('e+', 'e');
                exponent = 0;
                if ((pos = temp.indexOf('e')) > 0) {
                    exponent = +temp.slice(pos + 1);
                    temp = temp.slice(0, pos);
                }
                if (point >= 0) {
                    exponent -= temp.length - point - 1;
                    temp = +(temp.slice(0, point) + temp.slice(point + 1)) + '';
                }
                pos = 0;
                while (temp.charAt(temp.length + pos - 1) === '0') {
                    pos -= 1;
                }
                if (pos !== 0) {
                    exponent -= pos;
                    temp = temp.slice(0, pos);
                }
                if (exponent !== 0) {
                    temp += 'e' + exponent;
                }
                if ((temp.length < result.length ||
                    (hexadecimal && value > 1e12 && Math.floor(value) === value && (temp = '0x' + value.toString(16)).length < result.length)) &&
                    +temp === value) {
                    result = temp;
                }

                return result;
            }

            function escapeAllowedCharacter(ch, next) {
                var code = ch.charCodeAt(0), hex = code.toString(16), result = '\\';

                switch (ch) {
                    case '\b':
                        result += 'b';
                        break;
                    case '\f':
                        result += 'f';
                        break;
                    case '\t':
                        result += 't';
                        break;
                    default:
                        if (json || code > 0xff) {
                            result += 'u' + '0000'.slice(hex.length) + hex;
                        } else if (ch === '\u0000' && '0123456789'.indexOf(next) < 0) {
                            result += '0';
                        } else if (ch === '\v') {
                            result += 'v';
                        } else {
                            result += 'x' + '00'.slice(hex.length) + hex;
                        }
                        break;
                }

                return result;
            }

            function escapeDisallowedCharacter(ch) {
                var result = '\\';
                switch (ch) {
                    case '\\':
                        result += '\\';
                        break;
                    case '\n':
                        result += 'n';
                        break;
                    case '\r':
                        result += 'r';
                        break;
                    case '\u2028':
                        result += 'u2028';
                        break;
                    case '\u2029':
                        result += 'u2029';
                        break;
                    default:
                        throw new Error('Incorrectly classified character');
                }

                return result;
            }

            function escapeDirective(str) {
                var i, iz, ch, single, buf, quote;

                buf = str;
                if (typeof buf[0] === 'undefined') {
                    buf = stringToArray(buf);
                }

                quote = quotes === 'double' ? '"' : '\'';
                for (i = 0, iz = buf.length; i < iz; i += 1) {
                    ch = buf[i];
                    if (ch === '\'') {
                        quote = '"';
                        break;
                    } else if (ch === '"') {
                        quote = '\'';
                        break;
                    } else if (ch === '\\') {
                        i += 1;
                    }
                }

                return quote + str + quote;
            }

            function escapeString(str) {
                var result = '', i, len, ch, next, singleQuotes = 0, doubleQuotes = 0, single;

                if (typeof str[0] === 'undefined') {
                    str = stringToArray(str);
                }

                for (i = 0, len = str.length; i < len; i += 1) {
                    ch = str[i];
                    if (ch === '\'') {
                        singleQuotes += 1;
                    } else if (ch === '"') {
                        doubleQuotes += 1;
                    } else if (ch === '/' && json) {
                        result += '\\';
                    } else if ('\\\n\r\u2028\u2029'.indexOf(ch) >= 0) {
                        result += escapeDisallowedCharacter(ch);
                        continue;
                    } else if ((json && ch < ' ') || !(json || escapeless || (ch >= ' ' && ch <= '~'))) {
                        result += escapeAllowedCharacter(ch, str[i + 1]);
                        continue;
                    }
                    result += ch;
                }

                single = !(quotes === 'double' || (quotes === 'auto' && doubleQuotes < singleQuotes));
                str = result;
                result = single ? '\'' : '"';

                if (typeof str[0] === 'undefined') {
                    str = stringToArray(str);
                }

                for (i = 0, len = str.length; i < len; i += 1) {
                    ch = str[i];
                    if ((ch === '\'' && single) || (ch === '"' && !single)) {
                        result += '\\';
                    }
                    result += ch;
                }

                return result + (single ? '\'' : '"');
            }

            function isWhiteSpace(ch) {
                return '\t\v\f \xa0'.indexOf(ch) >= 0 || (ch.charCodeAt(0) >= 0x1680 && '\u1680\u180e\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u202f\u205f\u3000\ufeff'.indexOf(ch) >= 0);
            }

            function isLineTerminator(ch) {
                return '\n\r\u2028\u2029'.indexOf(ch) >= 0;
            }

            function isIdentifierPart(ch) {
                return (ch === '$') || (ch === '_') || (ch === '\\') ||
                    (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') ||
                    ((ch >= '0') && (ch <= '9')) ||
                    ((ch.charCodeAt(0) >= 0x80) && Regex.NonAsciiIdentifierPart.test(ch));
            }

            function toSourceNode(generated, node) {
                if (node == null) {
                    if (generated instanceof SourceNode) {
                        return generated;
                    } else {
                        node = {};
                    }
                }
                if (node.loc == null) {
                    return new SourceNode(null, null, sourceMap, generated);
                }
                return new SourceNode(node.loc.start.line, node.loc.start.column, (sourceMap === true ? node.loc.source || null : sourceMap), generated);
            }

            function join(left, right) {
                var leftSource = toSourceNode(left).toString(),
                    rightSource = toSourceNode(right).toString(),
                    leftChar = leftSource.charAt(leftSource.length - 1),
                    rightChar = rightSource.charAt(0);

                if (((leftChar === '+' || leftChar === '-') && leftChar === rightChar) || (isIdentifierPart(leftChar) && isIdentifierPart(rightChar))) {
                    return [left, ' ', right];
                } else if (isWhiteSpace(leftChar) || isLineTerminator(leftChar) || isWhiteSpace(rightChar) || isLineTerminator(rightChar)) {
                    return [left, right];
                }
                return [left, space, right];
            }

            function addIndent(stmt) {
                return [base, stmt];
            }

            function withIndent(fn) {
                var previousBase, result;
                previousBase = base;
                base += indent;
                result = fn.call(this, base);
                base = previousBase;
                return result;
            }

            function calculateSpaces(str) {
                var i;
                for (i = str.length - 1; i >= 0; i -= 1) {
                    if (isLineTerminator(str.charAt(i))) {
                        break;
                    }
                }
                return (str.length - 1) - i;
            }

            function adjustMultilineComment(value, specialBase) {
                var array, i, len, line, j, ch, spaces, previousBase;

                array = value.split(/\r\n|[\r\n]/);
                spaces = Number.MAX_VALUE;

                // first line doesn't have indentation
                for (i = 1, len = array.length; i < len; i += 1) {
                    line = array[i];
                    j = 0;
                    while (j < line.length && isWhiteSpace(line[j])) {
                        j += 1;
                    }
                    if (spaces > j) {
                        spaces = j;
                    }
                }

                if (typeof specialBase !== 'undefined') {
                    // pattern like
                    // {
                    //   var t = 20;  /*
                    //                 * this is comment
                    //                 */
                    // }
                    previousBase = base;
                    if (array[1][spaces] === '*') {
                        specialBase += ' ';
                    }
                    base = specialBase;
                } else {
                    if (spaces & 1) {
                        // /*
                        //  *
                        //  */
                        // If spaces are odd number, above pattern is considered.
                        // We waste 1 space.
                        spaces -= 1;
                    }
                    previousBase = base;
                }

                for (i = 1, len = array.length; i < len; i += 1) {
                    array[i] = toSourceNode(addIndent(array[i].slice(spaces))).join('');
                }

                base = previousBase;

                return array.join('\n');
            }

            function generateComment(comment, specialBase) {
                if (comment.type === 'Line') {
                    if (endsWithLineTerminator(comment.value)) {
                        return '//' + comment.value;
                    } else {
                        // Always use LineTerminator
                        return '//' + comment.value + '\n';
                    }
                }
                if (extra.format.indent.adjustMultilineComment && /[\n\r]/.test(comment.value)) {
                    return adjustMultilineComment('/*' + comment.value + '*/', specialBase);
                }
                return '/*' + comment.value + '*/';
            }

            function addCommentsToStatement(stmt, result) {
                var i, len, comment, save, node, tailingToStatement, specialBase, fragment;

                if (stmt.leadingComments && stmt.leadingComments.length > 0) {
                    save = result;

                    comment = stmt.leadingComments[0];
                    result = [];
                    if (safeConcatenation && stmt.type === Syntax.Program && stmt.body.length === 0) {
                        result.push('\n');
                    }
                    result.push(generateComment(comment));
                    if (!endsWithLineTerminator(toSourceNode(result).toString())) {
                        result.push('\n');
                    }

                    for (i = 1, len = stmt.leadingComments.length; i < len; i += 1) {
                        comment = stmt.leadingComments[i];
                        fragment = [generateComment(comment)];
                        if (!endsWithLineTerminator(toSourceNode(fragment).toString())) {
                            fragment.push('\n');
                        }
                        result.push(addIndent(fragment));
                    }

                    result.push(addIndent(save));
                }

                if (stmt.trailingComments) {
                    tailingToStatement = !endsWithLineTerminator(toSourceNode(result).toString());
                    specialBase = stringRepeat(' ', calculateSpaces(toSourceNode([base, result, indent]).toString()));
                    for (i = 0, len = stmt.trailingComments.length; i < len; i += 1) {
                        comment = stmt.trailingComments[i];
                        if (tailingToStatement) {
                            // We assume target like following script
                            //
                            // var t = 20;  /**
                            //               * This is comment of t
                            //               */
                            if (i === 0) {
                                // first case
                                result = [result, indent];
                            } else {
                                result = [result, specialBase];
                            }
                            result.push(generateComment(comment, specialBase));
                        } else {
                            result = [result, addIndent(generateComment(comment))];
                        }
                        if (i !== len - 1 && !endsWithLineTerminator(toSourceNode(result).toString())) {
                            result = [result, '\n'];
                        }
                    }
                }

                return result;
            }

            function parenthesize(text, current, should) {
                if (current < should) {
                    return ['(', text, ')'];
                }
                return text;
            }

            function maybeBlock(stmt, semicolonOptional, functionBody) {
                var result, noLeadingComment;

                noLeadingComment = !extra.comment || !stmt.leadingComments;

                if (stmt.type === Syntax.BlockStatement && noLeadingComment) {
                    return [space, generateStatement(stmt, { functionBody: functionBody })];
                }

                if (stmt.type === Syntax.EmptyStatement && noLeadingComment) {
                    return ';';
                }

                withIndent(function () {
                    result = [newline, addIndent(generateStatement(stmt, { semicolonOptional: semicolonOptional, functionBody: functionBody }))];
                });

                return result;
            }

            function maybeBlockSuffix(stmt, result) {
                var ends = endsWithLineTerminator(toSourceNode(result).toString());
                if (stmt.type === Syntax.BlockStatement && (!extra.comment || !stmt.leadingComments) && !ends) {
                    return [result, space];
                }
                if (ends) {
                    return [result, base];
                }
                return [result, newline, base];
            }

            function generateVerbatim(expr, option) {
                var i, result;
                result = expr[extra.verbatim].split(/\r\n|\n/);
                for (i = 1; i < result.length; i++) {
                    result[i] = newline + base + result[i];
                }

                result = parenthesize(result, Precedence.Sequence, option.precedence);
                return toSourceNode(result, expr);
            }

            function generateFunctionBody(node) {
                var result, i, len, expr;
                result = ['('];
                for (i = 0, len = node.params.length; i < len; i += 1) {
                    result.push(node.params[i].name);
                    if (i + 1 < len) {
                        result.push(',' + space);
                    }
                }
                result.push(')');

                if (node.expression) {
                    result.push(space);
                    expr = generateExpression(node.body, {
                        precedence: Precedence.Assignment,
                        allowIn: true,
                        allowCall: true
                    });
                    if (expr.toString().charAt(0) === '{') {
                        expr = ['(', expr, ')'];
                    }
                    result.push(expr);
                } else {
                    result.push(maybeBlock(node.body, false, true));
                }
                return result;
            }

            function generateExpression(expr, option) {
                var result, precedence, type, currentPrecedence, i, len, raw, fragment, multiline, leftChar, leftSource, rightChar, rightSource, allowIn, allowCall, allowUnparenthesizedNew, property, key, value;

                precedence = option.precedence;
                allowIn = option.allowIn;
                allowCall = option.allowCall;
                type = expr.type || option.type;

                if (extra.verbatim && expr.hasOwnProperty(extra.verbatim)) {
                    return generateVerbatim(expr, option);
                }

                switch (type) {
                    case Syntax.SequenceExpression:
                        result = [];
                        allowIn |= (Precedence.Sequence < precedence);
                        for (i = 0, len = expr.expressions.length; i < len; i += 1) {
                            result.push(generateExpression(expr.expressions[i], {
                                precedence: Precedence.Assignment,
                                allowIn: allowIn,
                                allowCall: true
                            }));
                            if (i + 1 < len) {
                                result.push(',' + space);
                            }
                        }
                        result = parenthesize(result, Precedence.Sequence, precedence);
                        break;

                    case Syntax.AssignmentExpression:
                        allowIn |= (Precedence.Assignment < precedence);
                        result = parenthesize(
                            [
                                generateExpression(expr.left, {
                                    precedence: Precedence.Call,
                                    allowIn: allowIn,
                                    allowCall: true
                                }),
                                space + expr.operator + space,
                                generateExpression(expr.right, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                })
                            ],
                            Precedence.Assignment,
                            precedence
                        );
                        break;

                    case Syntax.ConditionalExpression:
                        allowIn |= (Precedence.Conditional < precedence);
                        result = parenthesize(
                            [
                                generateExpression(expr.test, {
                                    precedence: Precedence.LogicalOR,
                                    allowIn: allowIn,
                                    allowCall: true
                                }),
                                space + '?' + space,
                                generateExpression(expr.consequent, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                }),
                                space + ':' + space,
                                generateExpression(expr.alternate, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                })
                            ],
                            Precedence.Conditional,
                            precedence
                        );
                        break;

                    case Syntax.LogicalExpression:
                    case Syntax.BinaryExpression:
                        currentPrecedence = BinaryPrecedence[expr.operator];

                        allowIn |= (currentPrecedence < precedence);

                        fragment = generateExpression(expr.left, {
                            precedence: currentPrecedence,
                            allowIn: allowIn,
                            allowCall: true
                        });

                        leftSource = fragment.toString();

                        if (leftSource.charAt(leftSource.length - 1) === '/' && isIdentifierPart(expr.operator.charAt(0))) {
                            result = [fragment, ' ', expr.operator];
                        } else {
                            result = join(fragment, expr.operator);
                        }

                        fragment = generateExpression(expr.right, {
                            precedence: currentPrecedence + 1,
                            allowIn: allowIn,
                            allowCall: true
                        });

                        if (expr.operator === '/' && fragment.toString().charAt(0) === '/') {
                            // If '/' concats with '/', it is interpreted as comment start
                            result.push(' ', fragment);
                        } else {
                            result = join(result, fragment);
                        }

                        if (expr.operator === 'in' && !allowIn) {
                            result = ['(', result, ')'];
                        } else {
                            result = parenthesize(result, currentPrecedence, precedence);
                        }

                        break;

                    case Syntax.CallExpression:
                        result = [generateExpression(expr.callee, {
                            precedence: Precedence.Call,
                            allowIn: true,
                            allowCall: true,
                            allowUnparenthesizedNew: false
                        })];

                        result.push('(');
                        for (i = 0, len = expr['arguments'].length; i < len; i += 1) {
                            result.push(generateExpression(expr['arguments'][i], {
                                precedence: Precedence.Assignment,
                                allowIn: true,
                                allowCall: true
                            }));
                            if (i + 1 < len) {
                                result.push(',' + space);
                            }
                        }
                        result.push(')');

                        if (!allowCall) {
                            result = ['(', result, ')'];
                        } else {
                            result = parenthesize(result, Precedence.Call, precedence);
                        }
                        break;

                    case Syntax.NewExpression:
                        len = expr['arguments'].length;
                        allowUnparenthesizedNew = option.allowUnparenthesizedNew === undefined || option.allowUnparenthesizedNew;

                        result = join(
                            'new',
                            generateExpression(expr.callee, {
                                precedence: Precedence.New,
                                allowIn: true,
                                allowCall: false,
                                allowUnparenthesizedNew: allowUnparenthesizedNew && !parentheses && len === 0
                            })
                        );

                        if (!allowUnparenthesizedNew || parentheses || len > 0) {
                            result.push('(');
                            for (i = 0; i < len; i += 1) {
                                result.push(generateExpression(expr['arguments'][i], {
                                    precedence: Precedence.Assignment,
                                    allowIn: true,
                                    allowCall: true
                                }));
                                if (i + 1 < len) {
                                    result.push(',' + space);
                                }
                            }
                            result.push(')');
                        }

                        result = parenthesize(result, Precedence.New, precedence);
                        break;

                    case Syntax.MemberExpression:
                        result = [generateExpression(expr.object, {
                            precedence: Precedence.Call,
                            allowIn: true,
                            allowCall: allowCall,
                            allowUnparenthesizedNew: false
                        })];

                        if (expr.computed) {
                            result.push('[', generateExpression(expr.property, {
                                precedence: Precedence.Sequence,
                                allowIn: true,
                                allowCall: allowCall
                            }), ']');
                        } else {
                            if (expr.object.type === Syntax.Literal && typeof expr.object.value === 'number') {
                                if (result.indexOf('.') < 0) {
                                    if (!/[eExX]/.test(result) && !(result.length >= 2 && result[0] === '0')) {
                                        result.push('.');
                                    }
                                }
                            }
                            result.push('.' + expr.property.name);
                        }

                        result = parenthesize(result, Precedence.Member, precedence);
                        break;

                    case Syntax.UnaryExpression:
                        fragment = generateExpression(expr.argument, {
                            precedence: Precedence.Unary,
                            allowIn: true,
                            allowCall: true
                        });

                        if (space === '') {
                            result = join(expr.operator, fragment);
                        } else {
                            result = [expr.operator];
                            if (expr.operator.length > 2) {
                                // delete, void, typeof
                                // get `typeof []`, not `typeof[]`
                                result = join(result, fragment);
                            } else {
                                // Prevent inserting spaces between operator and argument if it is unnecessary
                                // like, `!cond`
                                leftSource = toSourceNode(result).toString();
                                leftChar = leftSource.charAt(leftSource.length - 1);
                                rightChar = fragment.toString().charAt(0);

                                if (((leftChar === '+' || leftChar === '-') && leftChar === rightChar) || (isIdentifierPart(leftChar) && isIdentifierPart(rightChar))) {
                                    result.push(' ', fragment);
                                } else {
                                    result.push(fragment);
                                }
                            }
                        }
                        result = parenthesize(result, Precedence.Unary, precedence);
                        break;

                    case Syntax.YieldExpression:
                        if (expr.delegate) {
                            result = 'yield*';
                        } else {
                            result = 'yield';
                        }
                        if (expr.argument) {
                            result = join(
                                result,
                                generateExpression(expr.argument, {
                                    precedence: Precedence.Assignment,
                                    allowIn: true,
                                    allowCall: true
                                })
                            );
                        }
                        break;

                    case Syntax.UpdateExpression:
                        if (expr.prefix) {
                            result = parenthesize(
                                [
                                    expr.operator,
                                    generateExpression(expr.argument, {
                                        precedence: Precedence.Unary,
                                        allowIn: true,
                                        allowCall: true
                                    })
                                ],
                                Precedence.Unary,
                                precedence
                            );
                        } else {
                            result = parenthesize(
                                [
                                    generateExpression(expr.argument, {
                                        precedence: Precedence.Postfix,
                                        allowIn: true,
                                        allowCall: true
                                    }),
                                    expr.operator
                                ],
                                Precedence.Postfix,
                                precedence
                            );
                        }
                        break;

                    case Syntax.FunctionExpression:
                        result = 'function';
                        if (expr.id) {
                            result += ' ' + expr.id.name;
                        } else {
                            result += space;
                        }

                        result = [result, generateFunctionBody(expr)];
                        break;

                    case Syntax.ArrayPattern:
                    case Syntax.ArrayExpression:
                        if (!expr.elements.length) {
                            result = '[]';
                            break;
                        }
                        multiline = expr.elements.length > 1;
                        result = ['[', multiline ? newline : ''];
                        withIndent(function (indent) {
                            for (i = 0, len = expr.elements.length; i < len; i += 1) {
                                if (!expr.elements[i]) {
                                    if (multiline) {
                                        result.push(indent);
                                    }
                                    if (i + 1 === len) {
                                        result.push(',');
                                    }
                                } else {
                                    result.push(multiline ? indent : '', generateExpression(expr.elements[i], {
                                        precedence: Precedence.Assignment,
                                        allowIn: true,
                                        allowCall: true
                                    }));
                                }
                                if (i + 1 < len) {
                                    result.push(',' + (multiline ? newline : space));
                                }
                            }
                        });
                        if (multiline && !endsWithLineTerminator(toSourceNode(result).toString())) {
                            result.push(newline);
                        }
                        result.push(multiline ? base : '', ']');
                        break;

                    case Syntax.Property:
                        if (expr.kind === 'get' || expr.kind === 'set') {
                            result = [
                                expr.kind + ' ',
                                generateExpression(expr.key, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                generateFunctionBody(expr.value)
                            ];
                        } else {
                            if (expr.shorthand) {
                                result = generateExpression(expr.key, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                });
                            } else if (expr.method) {
                                result = [];
                                if (expr.value.generator) {
                                    result.push('*');
                                }
                                result.push(generateExpression(expr.key, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }), generateFunctionBody(expr.value));
                            } else {
                                result = [
                                    generateExpression(expr.key, {
                                        precedence: Precedence.Sequence,
                                        allowIn: true,
                                        allowCall: true
                                    }),
                                    ':' + space,
                                    generateExpression(expr.value, {
                                        precedence: Precedence.Assignment,
                                        allowIn: true,
                                        allowCall: true
                                    })
                                ];
                            }
                        }
                        break;

                    case Syntax.ObjectExpression:
                        if (!expr.properties.length) {
                            result = '{}';
                            break;
                        }
                        multiline = expr.properties.length > 1;

                        withIndent(function (indent) {
                            fragment = generateExpression(expr.properties[0], {
                                precedence: Precedence.Sequence,
                                allowIn: true,
                                allowCall: true,
                                type: Syntax.Property
                            });
                        });

                        if (!multiline) {
                            // issues 4
                            // Do not transform from
                            //   dejavu.Class.declare({
                            //       method2: function () {}
                            //   });
                            // to
                            //   dejavu.Class.declare({method2: function () {
                            //       }});
                            if (!hasLineTerminator(toSourceNode(fragment).toString())) {
                                result = [ '{', space, fragment, space, '}' ];
                                break;
                            }
                        }

                        withIndent(function (indent) {
                            result = [ '{', newline, indent, fragment ];

                            if (multiline) {
                                result.push(',' + newline);
                                for (i = 1, len = expr.properties.length; i < len; i += 1) {
                                    result.push(indent, generateExpression(expr.properties[i], {
                                        precedence: Precedence.Sequence,
                                        allowIn: true,
                                        allowCall: true,
                                        type: Syntax.Property
                                    }));
                                    if (i + 1 < len) {
                                        result.push(',' + newline);
                                    }
                                }
                            }
                        });

                        if (!endsWithLineTerminator(toSourceNode(result).toString())) {
                            result.push(newline);
                        }
                        result.push(base, '}');
                        break;

                    case Syntax.ObjectPattern:
                        if (!expr.properties.length) {
                            result = '{}';
                            break;
                        }

                        multiline = false;
                        if (expr.properties.length === 1) {
                            property = expr.properties[0];
                            if (property.value.type !== Syntax.Identifier) {
                                multiline = true;
                            }
                        } else {
                            for (i = 0, len = expr.properties.length; i < len; i += 1) {
                                property = expr.properties[i];
                                if (!property.shorthand) {
                                    multiline = true;
                                    break;
                                }
                            }
                        }
                        result = ['{', multiline ? newline : '' ];

                        withIndent(function (indent) {
                            for (i = 0, len = expr.properties.length; i < len; i += 1) {
                                result.push(multiline ? indent : '', generateExpression(expr.properties[i], {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }));
                                if (i + 1 < len) {
                                    result.push(',' + (multiline ? newline : space));
                                }
                            }
                        });

                        if (multiline && !endsWithLineTerminator(toSourceNode(result).toString())) {
                            result.push(newline);
                        }
                        result.push(multiline ? base : '', '}');
                        break;

                    case Syntax.ThisExpression:
                        result = 'this';
                        break;

                    case Syntax.Identifier:
                        result = expr.name;
                        break;

                    case Syntax.Literal:
                        if (expr.hasOwnProperty('raw') && parse) {
                            try {
                                raw = parse(expr.raw).body[0].expression;
                                if (raw.type === Syntax.Literal) {
                                    if (raw.value === expr.value) {
                                        result = expr.raw;
                                        break;
                                    }
                                }
                            } catch (e) {
                                // not use raw property
                            }
                        }

                        if (expr.value === null) {
                            result = 'null';
                            break;
                        }

                        if (typeof expr.value === 'string') {
                            result = escapeString(expr.value);
                            break;
                        }

                        if (typeof expr.value === 'number') {
                            result = generateNumber(expr.value);
                            break;
                        }

                        result = expr.value.toString();
                        break;

                    case Syntax.ComprehensionExpression:
                        result = [
                            '[',
                            generateExpression(expr.body, {
                                precedence: Precedence.Assignment,
                                allowIn: true,
                                allowCall: true
                            })
                        ];

                        if (expr.blocks) {
                            for (i = 0, len = expr.blocks.length; i < len; i += 1) {
                                fragment = generateExpression(expr.blocks[i], {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                });
                                result = join(result, fragment);
                            }
                        }

                        if (expr.filter) {
                            result = join(result, 'if' + space);
                            fragment = generateExpression(expr.filter, {
                                precedence: Precedence.Sequence,
                                allowIn: true,
                                allowCall: true
                            });
                            if (extra.moz.parenthesizedComprehensionBlock) {
                                result = join(result, [ '(', fragment, ')' ]);
                            } else {
                                result = join(result, fragment);
                            }
                        }
                        result.push(']');
                        break;

                    case Syntax.ComprehensionBlock:
                        if (expr.left.type === Syntax.VariableDeclaration) {
                            fragment = [
                                expr.left.kind + ' ',
                                generateStatement(expr.left.declarations[0], {
                                    allowIn: false
                                })
                            ];
                        } else {
                            fragment = generateExpression(expr.left, {
                                precedence: Precedence.Call,
                                allowIn: true,
                                allowCall: true
                            });
                        }

                        fragment = join(fragment, expr.of ? 'of' : 'in');
                        fragment = join(fragment, generateExpression(expr.right, {
                            precedence: Precedence.Sequence,
                            allowIn: true,
                            allowCall: true
                        }));

                        if (extra.moz.parenthesizedComprehensionBlock) {
                            result = [ 'for' + space + '(', fragment, ')' ];
                        } else {
                            result = join('for' + space, fragment);
                        }
                        break;

                    default:
                        console.log(expr);
                        throw new Error('Unknown expression type: ' + expr.type);
                }

                return toSourceNode(result, expr);
            }

            function generateStatement(stmt, option) {
                var i, len, result, node, allowIn, functionBody, directiveContext, fragment, semicolon;

                allowIn = true;
                semicolon = ';';
                functionBody = false;
                directiveContext = false;
                if (option) {
                    allowIn = option.allowIn === undefined || option.allowIn;
                    if (!semicolons && option.semicolonOptional === true) {
                        semicolon = '';
                    }
                    functionBody = option.functionBody;
                    directiveContext = option.directiveContext;
                }

                switch (stmt.type) {
                    case Syntax.BlockStatement:
                        result = ['{', newline];

                        withIndent(function () {
                            for (i = 0, len = stmt.body.length; i < len; i += 1) {
                                fragment = addIndent(generateStatement(stmt.body[i], {
                                    semicolonOptional: i === len - 1,
                                    directiveContext: functionBody
                                }));
                                result.push(fragment);
                                if (!endsWithLineTerminator(toSourceNode(fragment).toString())) {
                                    result.push(newline);
                                }
                            }
                        });

                        result.push(addIndent('}'));
                        break;

                    case Syntax.BreakStatement:
                        if (stmt.label) {
                            result = 'break ' + stmt.label.name + semicolon;
                        } else {
                            result = 'break' + semicolon;
                        }
                        break;

                    case Syntax.ContinueStatement:
                        if (stmt.label) {
                            result = 'continue ' + stmt.label.name + semicolon;
                        } else {
                            result = 'continue' + semicolon;
                        }
                        break;

                    case Syntax.DirectiveStatement:
                        if (stmt.raw) {
                            result = stmt.raw + semicolon;
                        } else {
                            result = escapeDirective(stmt.directive) + semicolon;
                        }
                        break;

                    case Syntax.DoWhileStatement:
                        // Because `do 42 while (cond)` is Syntax Error. We need semicolon.
                        result = join('do', maybeBlock(stmt.body));
                        result = maybeBlockSuffix(stmt.body, result);
                        result = join(result, [
                            'while' + space + '(',
                            generateExpression(stmt.test, {
                                precedence: Precedence.Sequence,
                                allowIn: true,
                                allowCall: true
                            }),
                            ')' + semicolon
                        ]);
                        break;

                    case Syntax.CatchClause:
                        withIndent(function () {
                            result = [
                                'catch' + space + '(',
                                generateExpression(stmt.param, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                ')'
                            ];
                        });
                        result.push(maybeBlock(stmt.body));
                        break;

                    case Syntax.DebuggerStatement:
                        result = 'debugger' + semicolon;
                        break;

                    case Syntax.EmptyStatement:
                        result = ';';
                        break;

                    case Syntax.ExpressionStatement:
                        result = [generateExpression(stmt.expression, {
                            precedence: Precedence.Sequence,
                            allowIn: true,
                            allowCall: true
                        })];
                        // 12.4 '{', 'function' is not allowed in this position.
                        // wrap expression with parentheses
                        if (result.toString().charAt(0) === '{' || (result.toString().slice(0, 8) === 'function' && " (".indexOf(result.toString().charAt(8)) >= 0) || (directive && directiveContext && stmt.expression.type === Syntax.Literal && typeof stmt.expression.value === 'string')) {
                            result = ['(', result, ')' + semicolon];
                        } else {
                            result.push(semicolon);
                        }
                        break;

                    case Syntax.VariableDeclarator:
                        if (stmt.init) {
                            var expr = generateExpression(stmt.id, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                });
                            result = [
                                generateExpression(stmt.id, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                }).toString() + space + '=' + space,
                                generateExpression(stmt.init, {
                                    precedence: Precedence.Assignment,
                                    allowIn: allowIn,
                                    allowCall: true
                                })
                            ];
                        } else {
                            result = stmt.id.name;
                        }
                        break;

                    case Syntax.VariableDeclaration:
                        result = [stmt.kind];
                        // special path for
                        // var x = function () {
                        // };
                        if (stmt.declarations.length === 1 && stmt.declarations[0].init &&
                            stmt.declarations[0].init.type === Syntax.FunctionExpression) {
                            result.push(' ', generateStatement(stmt.declarations[0], {
                                allowIn: allowIn
                            }));
                        } else {
                            // VariableDeclarator is typed as Statement,
                            // but joined with comma (not LineTerminator).
                            // So if comment is attached to target node, we should specialize.
                            withIndent(function () {
                                node = stmt.declarations[0];
                                if (extra.comment && node.leadingComments) {
                                    result.push('\n', addIndent(generateStatement(node, {
                                        allowIn: allowIn
                                    })));
                                } else {
                                    result.push(' ', generateStatement(node, {
                                        allowIn: allowIn
                                    }));
                                }

                                for (i = 1, len = stmt.declarations.length; i < len; i += 1) {
                                    node = stmt.declarations[i];
                                    if (extra.comment && node.leadingComments) {
                                        result.push(',' + newline, addIndent(generateStatement(node, {
                                            allowIn: allowIn
                                        })));
                                    } else {
                                        result.push(',' + space, generateStatement(node, {
                                            allowIn: allowIn
                                        }));
                                    }
                                }
                            });
                        }
                        result.push(semicolon);
                        break;

                    case Syntax.ThrowStatement:
                        result = [join(
                            'throw',
                            generateExpression(stmt.argument, {
                                precedence: Precedence.Sequence,
                                allowIn: true,
                                allowCall: true
                            })
                        ), semicolon];
                        break;

                    case Syntax.TryStatement:
                        result = ['try', maybeBlock(stmt.block)];
                        result = maybeBlockSuffix(stmt.block, result);
                        for (i = 0, len = stmt.handlers.length; i < len; i += 1) {
                            result = join(result, generateStatement(stmt.handlers[i]));
                            if (stmt.finalizer || i + 1 !== len) {
                                result = maybeBlockSuffix(stmt.handlers[i].body, result);
                            }
                        }
                        if (stmt.finalizer) {
                            result = join(result, ['finally', maybeBlock(stmt.finalizer)]);
                        }
                        break;

                    case Syntax.SwitchStatement:
                        withIndent(function () {
                            result = [
                                'switch' + space + '(',
                                generateExpression(stmt.discriminant, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                ')' + space + '{' + newline
                            ];
                        });
                        if (stmt.cases) {
                            for (i = 0, len = stmt.cases.length; i < len; i += 1) {
                                fragment = addIndent(generateStatement(stmt.cases[i], {semicolonOptional: i === len - 1}));
                                result.push(fragment);
                                if (!endsWithLineTerminator(toSourceNode(fragment).toString())) {
                                    result.push(newline);
                                }
                            }
                        }
                        result.push(addIndent('}'));
                        break;

                    case Syntax.SwitchCase:
                        withIndent(function () {
                            if (stmt.test) {
                                result = [
                                    join('case', generateExpression(stmt.test, {
                                        precedence: Precedence.Sequence,
                                        allowIn: true,
                                        allowCall: true
                                    })),
                                    ':'
                                ];
                            } else {
                                result = ['default:'];
                            }

                            i = 0;
                            len = stmt.consequent.length;
                            if (len && stmt.consequent[0].type === Syntax.BlockStatement) {
                                fragment = maybeBlock(stmt.consequent[0]);
                                result.push(fragment);
                                i = 1;
                            }

                            if (i !== len && !endsWithLineTerminator(toSourceNode(result).toString())) {
                                result.push(newline);
                            }

                            for (; i < len; i += 1) {
                                fragment = addIndent(generateStatement(stmt.consequent[i], {semicolonOptional: i === len - 1 && semicolon === ''}));
                                result.push(fragment);
                                if (i + 1 !== len && !endsWithLineTerminator(toSourceNode(fragment).toString())) {
                                    result.push(newline);
                                }
                            }
                        });
                        break;

                    case Syntax.IfStatement:
                        withIndent(function () {
                            result = [
                                'if' + space + '(',
                                generateExpression(stmt.test, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                ')'
                            ];
                        });
                        if (stmt.alternate) {
                            result.push(maybeBlock(stmt.consequent));
                            result = maybeBlockSuffix(stmt.consequent, result);
                            if (stmt.alternate.type === Syntax.IfStatement) {
                                result = join(result, ['else ', generateStatement(stmt.alternate, {semicolonOptional: semicolon === ''})]);
                            } else {
                                result = join(result, join('else', maybeBlock(stmt.alternate, semicolon === '')));
                            }
                        } else {
                            result.push(maybeBlock(stmt.consequent, semicolon === ''));
                        }
                        break;

                    case Syntax.ForStatement:
                        withIndent(function () {
                            result = ['for' + space + '('];
                            if (stmt.init) {
                                if (stmt.init.type === Syntax.VariableDeclaration) {
                                    result.push(generateStatement(stmt.init, {allowIn: false}));
                                } else {
                                    result.push(generateExpression(stmt.init, {
                                        precedence: Precedence.Sequence,
                                        allowIn: false,
                                        allowCall: true
                                    }), ';');
                                }
                            } else {
                                result.push(';');
                            }

                            if (stmt.test) {
                                result.push(space, generateExpression(stmt.test, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }), ';');
                            } else {
                                result.push(';');
                            }

                            if (stmt.update) {
                                result.push(space, generateExpression(stmt.update, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }), ')');
                            } else {
                                result.push(')');
                            }
                        });

                        result.push(maybeBlock(stmt.body, semicolon === ''));
                        break;

                    case Syntax.ForInStatement:
                        result = ['for' + space + '('];
                        withIndent(function () {
                            if (stmt.left.type === Syntax.VariableDeclaration) {
                                withIndent(function () {
                                    result.push(stmt.left.kind + ' ', generateStatement(stmt.left.declarations[0], {
                                        allowIn: false
                                    }));
                                });
                            } else {
                                result.push(generateExpression(stmt.left, {
                                    precedence: Precedence.Call,
                                    allowIn: true,
                                    allowCall: true
                                }));
                            }

                            result = join(result, 'in');
                            result = [join(
                                result,
                                generateExpression(stmt.right, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                })
                            ), ')'];
                        });
                        result.push(maybeBlock(stmt.body, semicolon === ''));
                        break;

                    case Syntax.LabeledStatement:
                        result = [stmt.label.name + ':', maybeBlock(stmt.body, semicolon === '')];
                        break;

                    case Syntax.Program:
                        len = stmt.body.length;
                        result = [safeConcatenation && len > 0 ? '\n' : ''];
                        for (i = 0; i < len; i += 1) {
                            fragment = addIndent(
                                generateStatement(stmt.body[i], {
                                    semicolonOptional: !safeConcatenation && i === len - 1,
                                    directiveContext: true
                                })
                            );
                            result.push(fragment);
                            if (i + 1 < len && !endsWithLineTerminator(toSourceNode(fragment).toString())) {
                                result.push(newline);
                            }
                        }
                        break;

                    case Syntax.FunctionDeclaration:
                        result = [(stmt.generator && !extra.moz.starlessGenerator ? 'function* ' : 'function ') + stmt.id.name, generateFunctionBody(stmt)];
                        break;

                    case Syntax.ReturnStatement:
                        if (stmt.argument) {
                            result = [join(
                                'return',
                                generateExpression(stmt.argument, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                })
                            ), semicolon];
                        } else {
                            result = ['return' + semicolon];
                        }
                        break;

                    case Syntax.WhileStatement:
                        withIndent(function () {
                            result = [
                                'while' + space + '(',
                                generateExpression(stmt.test, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                ')'
                            ];
                        });
                        result.push(maybeBlock(stmt.body, semicolon === ''));
                        break;

                    case Syntax.WithStatement:
                        withIndent(function () {
                            result = [
                                'with' + space + '(',
                                generateExpression(stmt.object, {
                                    precedence: Precedence.Sequence,
                                    allowIn: true,
                                    allowCall: true
                                }),
                                ')'
                            ];
                        });
                        result.push(maybeBlock(stmt.body, semicolon === ''));
                        break;

                    default:
                        throw new Error('Unknown statement type: ' + stmt.type);
                }

                // Attach comments

                if (extra.comment) {
                    result = addCommentsToStatement(stmt, result);
                }

                fragment = toSourceNode(result).toString();
                if (stmt.type === Syntax.Program && !safeConcatenation && newline === '' &&  fragment.charAt(fragment.length - 1) === '\n') {
                    result = toSourceNode(result).replaceRight(/\s+$/, '');
                }

                return toSourceNode(result, stmt);
            }

            function generate(node, options) {
                var defaultOptions = getDefaultOptions(), result, pair;

                if (options != null) {
                    // Obsolete options
                    //
                    //   `options.indent`
                    //   `options.base`
                    //
                    // Instead of them, we can use `option.format.indent`.
                    if (typeof options.indent === 'string') {
                        defaultOptions.format.indent.style = options.indent;
                    }
                    if (typeof options.base === 'number') {
                        defaultOptions.format.indent.base = options.base;
                    }
                    options = updateDeeply(defaultOptions, options);
                    indent = options.format.indent.style;
                    if (typeof options.base === 'string') {
                        base = options.base;
                    } else {
                        base = stringRepeat(indent, options.format.indent.base);
                    }
                } else {
                    options = defaultOptions;
                    indent = options.format.indent.style;
                    base = stringRepeat(indent, options.format.indent.base);
                }
                json = options.format.json;
                renumber = options.format.renumber;
                hexadecimal = json ? false : options.format.hexadecimal;
                quotes = json ? 'double' : options.format.quotes;
                escapeless = options.format.escapeless;
                if (options.format.compact) {
                    newline = space = indent = base = '';
                } else {
                    newline = '\n';
                    space = ' ';
                }
                parentheses = options.format.parentheses;
                semicolons = options.format.semicolons;
                safeConcatenation = options.format.safeConcatenation;
                directive = options.directive;
                parse = json ? null : options.parse;
                sourceMap = options.sourceMap;
                extra = options;

                if (sourceMap) {
                    if (!exports.browser) {
                        // We assume environment is node.js
                        // And prevent from including source-map by browserify
                        SourceNode = require('source-map').SourceNode;
                    } else {
                        SourceNode = global.sourceMap.SourceNode;
                    }
                } else {
                    SourceNode = SourceNodeMock;
                }

                switch (node.type) {
                    case Syntax.BlockStatement:
                    case Syntax.BreakStatement:
                    case Syntax.CatchClause:
                    case Syntax.ContinueStatement:
                    case Syntax.DirectiveStatement:
                    case Syntax.DoWhileStatement:
                    case Syntax.DebuggerStatement:
                    case Syntax.EmptyStatement:
                    case Syntax.ExpressionStatement:
                    case Syntax.ForStatement:
                    case Syntax.ForInStatement:
                    case Syntax.FunctionDeclaration:
                    case Syntax.IfStatement:
                    case Syntax.LabeledStatement:
                    case Syntax.Program:
                    case Syntax.ReturnStatement:
                    case Syntax.SwitchStatement:
                    case Syntax.SwitchCase:
                    case Syntax.ThrowStatement:
                    case Syntax.TryStatement:
                    case Syntax.VariableDeclaration:
                    case Syntax.VariableDeclarator:
                    case Syntax.WhileStatement:
                    case Syntax.WithStatement:
                        result = generateStatement(node);
                        break;

                    case Syntax.AssignmentExpression:
                    case Syntax.ArrayExpression:
                    case Syntax.ArrayPattern:
                    case Syntax.BinaryExpression:
                    case Syntax.CallExpression:
                    case Syntax.ConditionalExpression:
                    case Syntax.FunctionExpression:
                    case Syntax.Identifier:
                    case Syntax.Literal:
                    case Syntax.LogicalExpression:
                    case Syntax.MemberExpression:
                    case Syntax.NewExpression:
                    case Syntax.ObjectExpression:
                    case Syntax.ObjectPattern:
                    case Syntax.Property:
                    case Syntax.SequenceExpression:
                    case Syntax.ThisExpression:
                    case Syntax.UnaryExpression:
                    case Syntax.UpdateExpression:
                    case Syntax.YieldExpression:

                        result = generateExpression(node, {
                            precedence: Precedence.Sequence,
                            allowIn: true,
                            allowCall: true
                        });
                        break;

                    default:
                        throw new Error('Unknown node type: ' + node.type);
                }

                if (!sourceMap) {
                    return result.toString();
                }

                pair = result.toStringWithSourceMap({
                    file: options.sourceMap,
                    sourceRoot: options.sourceMapRoot
                });

                if (options.sourceMapWithCode) {
                    return pair;
                }
                return pair.map.toString();
            }

            // simple visitor implementation

            VisitorKeys = {
                AssignmentExpression: ['left', 'right'],
                ArrayExpression: ['elements'],
                ArrayPattern: ['elements'],
                BlockStatement: ['body'],
                BinaryExpression: ['left', 'right'],
                BreakStatement: ['label'],
                CallExpression: ['callee', 'arguments'],
                CatchClause: ['param', 'body'],
                ConditionalExpression: ['test', 'consequent', 'alternate'],
                ContinueStatement: ['label'],
                DirectiveStatement: [],
                DoWhileStatement: ['body', 'test'],
                DebuggerStatement: [],
                EmptyStatement: [],
                ExpressionStatement: ['expression'],
                ForStatement: ['init', 'test', 'update', 'body'],
                ForInStatement: ['left', 'right', 'body'],
                FunctionDeclaration: ['id', 'params', 'body'],
                FunctionExpression: ['id', 'params', 'body'],
                Identifier: [],
                IfStatement: ['test', 'consequent', 'alternate'],
                Literal: [],
                LabeledStatement: ['label', 'body'],
                LogicalExpression: ['left', 'right'],
                MemberExpression: ['object', 'property'],
                NewExpression: ['callee', 'arguments'],
                ObjectExpression: ['properties'],
                ObjectPattern: ['properties'],
                Program: ['body'],
                Property: ['key', 'value'],
                ReturnStatement: ['argument'],
                SequenceExpression: ['expressions'],
                SwitchStatement: ['discriminant', 'cases'],
                SwitchCase: ['test', 'consequent'],
                ThisExpression: [],
                ThrowStatement: ['argument'],
                TryStatement: ['block', 'handlers', 'finalizer'],
                UnaryExpression: ['argument'],
                UpdateExpression: ['argument'],
                VariableDeclaration: ['declarations'],
                VariableDeclarator: ['id', 'init'],
                WhileStatement: ['test', 'body'],
                WithStatement: ['object', 'body'],
                YieldExpression: ['argument']
            };

            VisitorOption = {
                Break: 1,
                Skip: 2
            };

            // based on LLVM libc++ upper_bound / lower_bound
            // MIT License

            function upperBound(array, func) {
                var diff, len, i, current;

                len = array.length;
                i = 0;

                while (len) {
                    diff = len >>> 1;
                    current = i + diff;
                    if (func(array[current])) {
                        len = diff;
                    } else {
                        i = current + 1;
                        len -= diff + 1;
                    }
                }
                return i;
            }

            function lowerBound(array, func) {
                var diff, len, i, current;

                len = array.length;
                i = 0;

                while (len) {
                    diff = len >>> 1;
                    current = i + diff;
                    if (func(array[current])) {
                        i = current + 1;
                        len -= diff + 1;
                    } else {
                        len = diff;
                    }
                }
                return i;
            }

            function extendCommentRange(comment, tokens) {
                var target, token;

                target = upperBound(tokens, function search(token) {
                    return token.range[0] > comment.range[0];
                });

                comment.extendedRange = [comment.range[0], comment.range[1]];

                if (target !== tokens.length) {
                    comment.extendedRange[1] = tokens[target].range[0];
                }

                target -= 1;
                if (target >= 0) {
                    if (target < tokens.length) {
                        comment.extendedRange[0] = tokens[target].range[1];
                    } else if (token.length) {
                        comment.extendedRange[1] = tokens[tokens.length - 1].range[0];
                    }
                }

                return comment;
            }

            function attachComments(tree, providedComments, tokens) {
                // At first, we should calculate extended comment ranges.
                var comments = [], comment, len, i;

                if (!tree.range) {
                    throw new Error('attachComments needs range information');
                }

                // tokens array is empty, we attach comments to tree as 'leadingComments'
                if (!tokens.length) {
                    if (providedComments.length) {
                        for (i = 0, len = providedComments.length; i < len; i += 1) {
                            comment = deepCopy(providedComments[i]);
                            comment.extendedRange = [0, tree.range[0]];
                            comments.push(comment);
                        }
                        tree.leadingComments = comments;
                    }
                    return tree;
                }

                for (i = 0, len = providedComments.length; i < len; i += 1) {
                    comments.push(extendCommentRange(deepCopy(providedComments[i]), tokens));
                }

                // This is based on John Freeman's implementation.
                traverse(tree, {
                    cursor: 0,
                    enter: function (node) {
                        var comment;

                        while (this.cursor < comments.length) {
                            comment = comments[this.cursor];
                            if (comment.extendedRange[1] > node.range[0]) {
                                break;
                            }

                            if (comment.extendedRange[1] === node.range[0]) {
                                if (!node.leadingComments) {
                                    node.leadingComments = [];
                                }
                                node.leadingComments.push(comment);
                                comments.splice(this.cursor, 1);
                            } else {
                                this.cursor += 1;
                            }
                        }

                        // already out of owned node
                        if (this.cursor === comments.length) {
                            return VisitorOption.Break;
                        }

                        if (comments[this.cursor].extendedRange[0] > node.range[1]) {
                            return VisitorOption.Skip;
                        }
                    }
                });

                traverse(tree, {
                    cursor: 0,
                    leave: function (node) {
                        var comment;

                        while (this.cursor < comments.length) {
                            comment = comments[this.cursor];
                            if (node.range[1] < comment.extendedRange[0]) {
                                break;
                            }

                            if (node.range[1] === comment.extendedRange[0]) {
                                if (!node.trailingComments) {
                                    node.trailingComments = [];
                                }
                                node.trailingComments.push(comment);
                                comments.splice(this.cursor, 1);
                            } else {
                                this.cursor += 1;
                            }
                        }

                        // already out of owned node
                        if (this.cursor === comments.length) {
                            return VisitorOption.Break;
                        }

                        if (comments[this.cursor].extendedRange[0] > node.range[1]) {
                            return VisitorOption.Skip;
                        }
                    }
                });

                return tree;
            }

            // Sync with package.json.
            exports.version = '0.0.17';

            exports.generate = generate;
            exports.attachComments = attachComments;
            exports.browser = false;
        }());
        /* vim: set sw=4 ts=4 et tw=80 : */

    });

    require.define("/node_modules/estraverse/package.json",function(require,module,exports,__dirname,__filename,process,global){module.exports = {"main":"estraverse.js"}
    });

    require.define("/node_modules/estraverse/estraverse.js",function(require,module,exports,__dirname,__filename,process,global){/*
     Copyright (C) 2012 Yusuke Suzuki <<EMAIL>>
     Copyright (C) 2012 Ariya Hidayat <<EMAIL>>

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are met:

     * Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

     THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
     AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
     IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
     ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
     DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
     THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */

        /*jslint bitwise:true */
        /*global exports:true, define:true, window:true */
        (function (factory) {
            'use strict';

            // Universal Module Definition (UMD) to support AMD, CommonJS/Node.js,
            // and plain browser loading,
            if (typeof define === 'function' && define.amd) {
                define(['exports'], factory);
            } else if (typeof exports !== 'undefined') {
                factory(exports);
            } else {
                factory((window.estraverse = {}));
            }
        }(function (exports) {
            'use strict';

            var Syntax,
                isArray,
                VisitorOption,
                VisitorKeys,
                wrappers;

            Syntax = {
                AssignmentExpression: 'AssignmentExpression',
                ArrayExpression: 'ArrayExpression',
                BlockStatement: 'BlockStatement',
                BinaryExpression: 'BinaryExpression',
                BreakStatement: 'BreakStatement',
                CallExpression: 'CallExpression',
                CatchClause: 'CatchClause',
                ConditionalExpression: 'ConditionalExpression',
                ContinueStatement: 'ContinueStatement',
                DebuggerStatement: 'DebuggerStatement',
                DirectiveStatement: 'DirectiveStatement',
                DoWhileStatement: 'DoWhileStatement',
                EmptyStatement: 'EmptyStatement',
                ExpressionStatement: 'ExpressionStatement',
                ForStatement: 'ForStatement',
                ForInStatement: 'ForInStatement',
                FunctionDeclaration: 'FunctionDeclaration',
                FunctionExpression: 'FunctionExpression',
                Identifier: 'Identifier',
                IfStatement: 'IfStatement',
                Literal: 'Literal',
                LabeledStatement: 'LabeledStatement',
                LogicalExpression: 'LogicalExpression',
                MemberExpression: 'MemberExpression',
                NewExpression: 'NewExpression',
                ObjectExpression: 'ObjectExpression',
                Program: 'Program',
                Property: 'Property',
                ReturnStatement: 'ReturnStatement',
                SequenceExpression: 'SequenceExpression',
                SwitchStatement: 'SwitchStatement',
                SwitchCase: 'SwitchCase',
                ThisExpression: 'ThisExpression',
                ThrowStatement: 'ThrowStatement',
                TryStatement: 'TryStatement',
                UnaryExpression: 'UnaryExpression',
                UpdateExpression: 'UpdateExpression',
                VariableDeclaration: 'VariableDeclaration',
                VariableDeclarator: 'VariableDeclarator',
                WhileStatement: 'WhileStatement',
                WithStatement: 'WithStatement'
            };

            isArray = Array.isArray;
            if (!isArray) {
                isArray = function isArray(array) {
                    return Object.prototype.toString.call(array) === '[object Array]';
                };
            }

            VisitorKeys = {
                AssignmentExpression: ['left', 'right'],
                ArrayExpression: ['elements'],
                BlockStatement: ['body'],
                BinaryExpression: ['left', 'right'],
                BreakStatement: ['label'],
                CallExpression: ['callee', 'arguments'],
                CatchClause: ['param', 'body'],
                ConditionalExpression: ['test', 'consequent', 'alternate'],
                ContinueStatement: ['label'],
                DebuggerStatement: [],
                DirectiveStatement: [],
                DoWhileStatement: ['body', 'test'],
                EmptyStatement: [],
                ExpressionStatement: ['expression'],
                ForStatement: ['init', 'test', 'update', 'body'],
                ForInStatement: ['left', 'right', 'body'],
                FunctionDeclaration: ['id', 'params', 'body'],
                FunctionExpression: ['id', 'params', 'body'],
                Identifier: [],
                IfStatement: ['test', 'consequent', 'alternate'],
                Literal: [],
                LabeledStatement: ['label', 'body'],
                LogicalExpression: ['left', 'right'],
                MemberExpression: ['object', 'property'],
                NewExpression: ['callee', 'arguments'],
                ObjectExpression: ['properties'],
                Program: ['body'],
                Property: ['key', 'value'],
                ReturnStatement: ['argument'],
                SequenceExpression: ['expressions'],
                SwitchStatement: ['discriminant', 'cases'],
                SwitchCase: ['test', 'consequent'],
                ThisExpression: [],
                ThrowStatement: ['argument'],
                TryStatement: ['block', 'handlers', 'finalizer'],
                UnaryExpression: ['argument'],
                UpdateExpression: ['argument'],
                VariableDeclaration: ['declarations'],
                VariableDeclarator: ['id', 'init'],
                WhileStatement: ['test', 'body'],
                WithStatement: ['object', 'body']
            };

            VisitorOption = {
                Break: 1,
                Skip: 2
            };

            wrappers = {
                PropertyWrapper: 'Property'
            };

            function traverse(top, visitor) {
                var worklist, leavelist, node, nodeType, ret, current, current2, candidates, candidate, marker = {};

                worklist = [ top ];
                leavelist = [ null ];

                while (worklist.length) {
                    node = worklist.pop();
                    nodeType = node.type;

                    if (node === marker) {
                        node = leavelist.pop();
                        if (visitor.leave) {
                            ret = visitor.leave(node, leavelist[leavelist.length - 1]);
                        } else {
                            ret = undefined;
                        }
                        if (ret === VisitorOption.Break) {
                            return;
                        }
                    } else if (node) {
                        if (wrappers.hasOwnProperty(nodeType)) {
                            node = node.node;
                            nodeType = wrappers[nodeType];
                        }

                        if (visitor.enter) {
                            ret = visitor.enter(node, leavelist[leavelist.length - 1]);
                        } else {
                            ret = undefined;
                        }

                        if (ret === VisitorOption.Break) {
                            return;
                        }

                        worklist.push(marker);
                        leavelist.push(node);

                        if (ret !== VisitorOption.Skip) {
                            candidates = VisitorKeys[nodeType];
                            current = candidates.length;
                            while ((current -= 1) >= 0) {
                                candidate = node[candidates[current]];
                                if (candidate) {
                                    if (isArray(candidate)) {
                                        current2 = candidate.length;
                                        while ((current2 -= 1) >= 0) {
                                            if (candidate[current2]) {
                                                if(nodeType === Syntax.ObjectExpression && 'properties' === candidates[current] && null == candidates[current].type) {
                                                    worklist.push({type: 'PropertyWrapper', node: candidate[current2]});
                                                } else {
                                                    worklist.push(candidate[current2]);
                                                }
                                            }
                                        }
                                    } else {
                                        worklist.push(candidate);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            function replace(top, visitor) {
                var worklist, leavelist, node, nodeType, target, tuple, ret, current, current2, candidates, candidate, marker = {}, result;

                result = {
                    top: top
                };

                tuple = [ top, result, 'top' ];
                worklist = [ tuple ];
                leavelist = [ tuple ];

                function notify(v) {
                    ret = v;
                }

                while (worklist.length) {
                    tuple = worklist.pop();

                    if (tuple === marker) {
                        tuple = leavelist.pop();
                        ret = undefined;
                        if (visitor.leave) {
                            node = tuple[0];
                            target = visitor.leave(tuple[0], leavelist[leavelist.length - 1][0], notify);
                            if (target !== undefined) {
                                node = target;
                            }
                            tuple[1][tuple[2]] = node;
                        }
                        if (ret === VisitorOption.Break) {
                            return result.top;
                        }
                    } else if (tuple[0]) {
                        ret = undefined;
                        node = tuple[0];

                        nodeType = node.type;
                        if (wrappers.hasOwnProperty(nodeType)) {
                            tuple[0] = node = node.node;
                            nodeType = wrappers[nodeType];
                        }

                        if (visitor.enter) {
                            target = visitor.enter(tuple[0], leavelist[leavelist.length - 1][0], notify);
                            if (target !== undefined) {
                                node = target;
                            }
                            tuple[1][tuple[2]] = node;
                            tuple[0] = node;
                        }

                        if (ret === VisitorOption.Break) {
                            return result.top;
                        }

                        if (tuple[0]) {
                            worklist.push(marker);
                            leavelist.push(tuple);

                            if (ret !== VisitorOption.Skip) {
                                candidates = VisitorKeys[nodeType];
                                current = candidates.length;
                                while ((current -= 1) >= 0) {
                                    candidate = node[candidates[current]];
                                    if (candidate) {
                                        if (isArray(candidate)) {
                                            current2 = candidate.length;
                                            while ((current2 -= 1) >= 0) {
                                                if (candidate[current2]) {
                                                    if(nodeType === Syntax.ObjectExpression && 'properties' === candidates[current] && null == candidates[current].type) {
                                                        worklist.push([{type: 'PropertyWrapper', node: candidate[current2]}, candidate, current2]);
                                                    } else {
                                                        worklist.push([candidate[current2], candidate, current2]);
                                                    }
                                                }
                                            }
                                        } else {
                                            worklist.push([candidate, node, candidates[current]]);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                return result.top;
            }

            exports.version = '0.0.4';
            exports.Syntax = Syntax;
            exports.traverse = traverse;
            exports.replace = replace;
            exports.VisitorKeys = VisitorKeys;
            exports.VisitorOption = VisitorOption;
        }));
        /* vim: set sw=4 ts=4 et tw=80 : */

    });

    require.define("/tools/entry-point.js",function(require,module,exports,__dirname,__filename,process,global){/*
     Copyright (C) 2012 Yusuke Suzuki <<EMAIL>>

     Redistribution and use in source and binary forms, with or without
     modification, are permitted provided that the following conditions are met:

     * Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.
     * Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

     THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
     AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
     IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
     ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
     DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
     (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
     LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
     (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
     THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
     */

        (function () {
            'use strict';
            var escodegen;
            escodegen = require('../escodegen');
            $.__bodymovin.bm_expressionHelper.escodegen = escodegen;
            escodegen.browser = true;
        }());

    });
    require("/tools/entry-point.js");
})();