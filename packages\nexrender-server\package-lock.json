{"name": "@nexrender/server", "version": "1.62.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@nexrender/server", "version": "1.46.9", "dependencies": {"@nexrender/database-redis": "^1.45.6", "@nexrender/types": "^1.45.6", "arg": "^4.1.0", "async-mutex": "^0.3.2", "chalk": "^2.4.2", "micro": "~9.3.3", "micro-cors": "^0.1.1", "microrouter": "^3.1.3", "requireg": "^0.2.2"}, "bin": {"nexrender-server": "src/bin.js"}, "peerDependencies": {"@nexrender/database-redis": "^1.0.0"}}, "../nexrender-database-redis": {"name": "@nexrender/database-redis", "version": "1.45.6", "dependencies": {"redis": "^3.1.2"}}, "../nexrender-database-redis/node_modules/denque": {"version": "1.5.1", "license": "Apache-2.0", "engines": {"node": ">=0.10"}}, "../nexrender-database-redis/node_modules/redis": {"version": "3.1.2", "license": "MIT", "dependencies": {"denque": "^1.5.0", "redis-commands": "^1.7.0", "redis-errors": "^1.2.0", "redis-parser": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-redis"}}, "../nexrender-database-redis/node_modules/redis-commands": {"version": "1.7.0", "license": "MIT"}, "../nexrender-database-redis/node_modules/redis-errors": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "../nexrender-database-redis/node_modules/redis-parser": {"version": "3.0.0", "license": "MIT", "dependencies": {"redis-errors": "^1.0.0"}, "engines": {"node": ">=4"}}, "../nexrender-types": {"name": "@nexrender/types", "version": "1.45.6", "dependencies": {"nanoid": "^3.2.0"}}, "../nexrender-types/node_modules/nanoid": {"version": "3.3.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/@nexrender/database-redis": {"resolved": "../nexrender-database-redis", "link": true}, "node_modules/@nexrender/types": {"resolved": "../nexrender-types", "link": true}, "node_modules/ansi-styles": {"version": "3.2.1", "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/arg": {"version": "4.1.3", "license": "MIT"}, "node_modules/async-mutex": {"version": "0.3.2", "license": "MIT", "dependencies": {"tslib": "^2.3.1"}}, "node_modules/bytes": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/chalk": {"version": "2.4.2", "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/color-convert": {"version": "1.9.3", "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "license": "MIT"}, "node_modules/content-type": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/deep-extend": {"version": "0.6.0", "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/depd": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/has-flag": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/http-errors": {"version": "1.6.2", "license": "MIT", "dependencies": {"depd": "1.1.1", "inherits": "2.0.3", "setprototypeof": "1.0.3", "statuses": ">= 1.3.1 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/iconv-lite": {"version": "0.4.19", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "license": "ISC"}, "node_modules/is-stream": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/micro": {"version": "9.3.4", "license": "MIT", "dependencies": {"arg": "4.1.0", "content-type": "1.0.4", "is-stream": "1.1.0", "raw-body": "2.3.2"}, "bin": {"micro": "bin/micro.js"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/micro-cors": {"version": "0.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/micro/node_modules/arg": {"version": "4.1.0", "license": "MIT"}, "node_modules/microrouter": {"version": "3.1.3", "license": "MIT", "dependencies": {"url-pattern": "^1.0.3"}, "engines": {"node": ">=6.10.0"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/nested-error-stacks": {"version": "2.0.1", "license": "MIT"}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/raw-body": {"version": "2.3.2", "license": "MIT", "dependencies": {"bytes": "3.0.0", "http-errors": "1.6.2", "iconv-lite": "0.4.19", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/rc": {"version": "1.2.8", "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/requireg": {"version": "0.2.2", "dependencies": {"nested-error-stacks": "~2.0.1", "rc": "~1.2.7", "resolve": "~1.7.1"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/resolve": {"version": "1.7.1", "license": "MIT", "dependencies": {"path-parse": "^1.0.5"}}, "node_modules/setprototypeof": {"version": "1.0.3", "license": "ISC"}, "node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/supports-color": {"version": "5.5.0", "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/tslib": {"version": "2.6.2", "license": "0BSD"}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/url-pattern": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">=0.12.0"}}}}